#include "main.h"
#include "gui.h"
#include "communication/CommunicationManager.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "esp_log.h"
#include "potentiometer_system.h"
#include "mcp23017_switches.h"

// Déclaration des handles de tâches
static TaskHandle_t comm_task_handle = NULL;
static TaskHandle_t lvgl_task_handle = NULL; // Nouveau handle pour la tâche LVGL

// Fonction de tâche pour gérer la communication
static void communication_task(void *pvParameters) {
    CommunicationManager *comm = (CommunicationManager *)pvParameters;

    // Réduire les logs dans la tâche de communication mais préserver les logs du potentiomètre et des switchs
    esp_log_level_set("*", ESP_LOG_ERROR);
    esp_log_level_set("PotSystem", ESP_LOG_INFO);
    esp_log_level_set("MCP23017Switches", ESP_LOG_INFO);

    while (1) {
        // Verrouiller l'accès à l'affichage/LVGL
        bsp_display_lock(0);

        // Mettre à jour le gestionnaire de communication
        comm->update();

        // Déverrouiller l'accès
        bsp_display_unlock();

        // Délai court pour ne pas surcharger le CPU et permettre à d'autres tâches de s'exécuter
        vTaskDelay(pdMS_TO_TICKS(10)); // Utiliser pdMS_TO_TICKS pour la clarté
    }
}

// Nouvelle fonction de tâche pour gérer LVGL
static void lvgl_task(void *pvParameters) {
    while(1) {
        // Verrouiller l'accès à l'affichage/LVGL (attente max 10ms)
        if (bsp_display_lock(pdMS_TO_TICKS(10))) { // Attente limitée pour éviter blocage total
            // Exécuter le handler principal de LVGL
            uint32_t time_till_next = lv_timer_handler();

            // Déverrouiller l'accès
            bsp_display_unlock();

            // Calculer le délai basé sur la valeur retournée par lv_timer_handler
            // Assurer un délai minimum pour éviter une boucle trop serrée
            if (time_till_next == LV_NO_TIMER_READY) {
                time_till_next = 5; // Délai par défaut si aucun timer LVGL n'est prêt
            } else if (time_till_next > 500) {
                 time_till_next = 500; // Limiter le délai maximum
            } else if (time_till_next < 5) {
                 time_till_next = 5; // Assurer un délai minimum
            }
             vTaskDelay(pdMS_TO_TICKS(time_till_next));
        } else {
            // N'a pas pu obtenir le verrou, réessayer après un court délai
             vTaskDelay(pdMS_TO_TICKS(5));
        }
    }
}

MainApplication::MainApplication() {
    display_config = {
        .lvgl_port_cfg = ESP_LVGL_PORT_INIT_CONFIG(),
        .buffer_size = BSP_LCD_DRAW_BUFF_SIZE,
        .double_buffer = BSP_LCD_DRAW_BUFF_DOUBLE,
        .flags = {
            .buff_dma = true,
            .buff_spiram = false,
            .sw_rotate = false,
        }
    };
}

void MainApplication::initialize() {
    // Réduire les logs globaux à ERROR seulement, mais préserver les logs du potentiomètre et des switchs
    esp_log_level_set("*", ESP_LOG_ERROR);
    esp_log_level_set("PotSystem", ESP_LOG_INFO);
    esp_log_level_set("MCP23017Switches", ESP_LOG_INFO);

    // Initialiser le gestionnaire de communication
    initCommunicationManager();

    // Récupérer l'instance de CommunicationManager
    extern CommunicationManager* comm_instance;

    // Initialiser l'affichage AVANT de créer les tâches qui l'utilisent
    bsp_display_start_with_config(&display_config);
    bsp_display_backlight_on();

    // Initialiser l'interface graphique AVANT de démarrer les tâches qui pourraient l'utiliser
    bsp_display_lock(0);
    gui_init();
    bsp_display_unlock();

    // Créer la tâche de communication
    if (comm_instance != NULL) {
        xTaskCreate(communication_task, "comm_task", 4096, comm_instance, 5, &comm_task_handle);
    }

    // Créer la tâche LVGL dédiée
    xTaskCreate(lvgl_task, "lvgl_task", 4096, NULL, configMAX_PRIORITIES - 1, &lvgl_task_handle); // Priorité élevée
}

void MainApplication::run() {
    // gui_init() est maintenant appelé dans initialize()
    // La fonction run est maintenant vide car les tâches gèrent l'exécution
}

extern "C" void app_main(void) {
    // Réduire les logs dès le début
    esp_log_level_set("*", ESP_LOG_ERROR);

    // Activer les logs INFO pour le système de potentiomètres et des switchs
    esp_log_level_set("PotSystem", ESP_LOG_INFO);
    esp_log_level_set("MCP23017Switches", ESP_LOG_INFO);

    MainApplication app;
    
    // Initialiser le système de potentiomètres
    potentiometer_system_init();

    // Initialiser le système de switchs MCP23017
    mcp23017_switches_init();

    // Par défaut, les logs des potentiomètres sont activés
    // Pour les désactiver, décommentez la ligne suivante :
    // potentiometer_logs_enable(false);

    // Par défaut, les logs des switchs sont activés
    // Pour les désactiver, décommentez la ligne suivante :
    // mcp23017_switches_logs_enable(false);

    app.initialize();
    // app.run() n'est plus nécessaire ici car tout est géré par les tâches FreeRTOS

    // La tâche principale peut maintenant se terminer ou faire autre chose si nécessaire.
    // Pour un système basé sur FreeRTOS, il est courant de laisser les tâches tourner indéfiniment.
}