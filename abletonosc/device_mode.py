from typing import Dict, Any, Callable
import logging
import traceback
import json
import unicodedata


# Import des modules refactorisés
from .device_mode_helper.device_mode_lock_methods import (
    start_locked_device_listeners,
    stop_locked_device_listeners,
    send_locked_device_reference,
    _find_device_track,
    deviceLock_handler,
    verify_lock_device_validity
)

from .device_mode_helper.device_mode_device_listeners import (
    _setup_device_listeners,
    _stop_device_listeners,
    start_selected_device_listeners,
    stop_selected_device_listeners
)

from .device_mode_helper.device_mode_other_listeners import (
    _create_chain_selection_callback,
    _setup_rack_listeners_recursive
)

from .device_mode_helper.device_mode_actions_on_device import (
    toggle_device_active,
    toggle_device_active_by_path,
    delete_device_by_path,
    move_device_by_path,
    set_device_parameter_by_path
)

from .device_mode_helper.device_mode_chain_listeners import (
    _setup_chain_listeners,
    _stop_chain_listeners,
    _get_current_environment_data
)

from .device_mode_helper.device_mode_environment import (
    _get_chain_status,
    _get_device_status,
    _build_chains_data,
    _build_devices_data,
    send_chain_environment,
    send_device_environment,
    _send_environment_data,
    _get_chain_path_indices,
    send_drum_grid_state,
    _build_path_string
)

from .device_mode_helper.device_mode_utils import (
    _normalize_name
)

class DeviceMode:
    def __init__(self, device_handler):
        self.device_handler = device_handler
        self.manager = device_handler.manager
        self.osc_server = device_handler.osc_server
        self.song = device_handler.song
        self.logger = logging.getLogger("abletonosc")

        self.deviceMode_listeners = {}
        self.deviceLockMode_listeners = {}
        self.chain_listeners = {}  # Nouveau dictionnaire pour les listeners des chaînes
        self.navigation_path = []  # [(type, index, name), ...]
        self.last_environment_data = None  # Pour stocker le dernier état
        self.current_rack = None  # Pour stocker le rack actuellement surveillé

        # Nouveaux attributs pour stocker le dernier appel
        self.last_call_type = None  # 'select_rack_chain' ou 'select_chain_device'
        self.last_selected_chain = None  # Dernière chaîne sélectionnée
        self.last_selected_device = None  # Dernier device sélectionné
        self.last_call_params = None  # Paramètres du dernier appel

        # Buffers pour les paramètres quantifiés (pour ralentir les changements)
        self.quantized_param_buffers = {}  # {parameter_id: accumulated_delta}

    # Intégration des méthodes importées
    start_locked_device_listeners = start_locked_device_listeners
    stop_locked_device_listeners = stop_locked_device_listeners
    send_locked_device_reference = send_locked_device_reference
    _find_device_track = _find_device_track
    deviceLock_handler = deviceLock_handler
    verify_lock_device_validity = verify_lock_device_validity
    _setup_device_listeners = _setup_device_listeners
    _stop_device_listeners = _stop_device_listeners
    start_selected_device_listeners = start_selected_device_listeners
    stop_selected_device_listeners = stop_selected_device_listeners
    _create_chain_selection_callback = _create_chain_selection_callback
    _setup_rack_listeners_recursive = _setup_rack_listeners_recursive
    toggle_device_active = toggle_device_active
    toggle_device_active_by_path = toggle_device_active_by_path
    delete_device_by_path = delete_device_by_path
    move_device_by_path = move_device_by_path
    set_device_parameter_by_path = set_device_parameter_by_path
    _setup_chain_listeners = _setup_chain_listeners
    _stop_chain_listeners = _stop_chain_listeners
    _get_current_environment_data = _get_current_environment_data
    _get_chain_status = _get_chain_status
    _get_device_status = _get_device_status
    _build_chains_data = _build_chains_data
    _build_devices_data = _build_devices_data
    send_chain_environment = send_chain_environment
    send_device_environment = send_device_environment
    _send_environment_data = _send_environment_data
    _get_chain_path_indices = _get_chain_path_indices
    send_drum_grid_state = send_drum_grid_state
    _build_path_string = _build_path_string
    _normalize_name = _normalize_name

    def select_rack_chain(self, params):
        try:
            if not params or len(params) % 2 != 0:
                self.logger.warning("Il faut un nombre pair de paramètres: [rack_index, chain_index, ...]")
                return

            self.logger.info(f"Chemin reçu: {params}")

            selected_track = self.song.view.selected_track
            if not selected_track:
                self.logger.warning("Pas de piste sélectionnée")
                return

            # Définir le flag avant toute opération
            self.manager.is_selecting_rack_chain = True
            self.logger.info("Définir le flag is_selecting_rack_chain à True")

            try:
                current_container = selected_track.devices
                current_rack = None
                final_chain = None

                for i in range(0, len(params), 2):
                    rack_index = int(params[i])
                    chain_index = int(params[i + 1])

                    if rack_index < 0 or rack_index >= len(current_container):
                        self.logger.warning(f"Index de rack invalide: {rack_index}")
                        return

                    current_rack = current_container[rack_index]

                    if not hasattr(current_rack, 'chains'):
                        self.logger.warning(f"Le device {current_rack.name} n'est pas un rack")
                        return

                    if chain_index < 0 or chain_index >= len(current_rack.chains):
                        self.logger.warning(f"Index de chaîne invalide: {chain_index}")
                        return

                    chain = current_rack.chains[chain_index]
                    current_rack.view.selected_chain = chain

                    final_chain = chain
                    current_container = chain.devices

                # Configurer les listeners avec le chemin complet
                if final_chain:
                    self._setup_chain_listeners(final_chain, params)

                if current_rack:
                    self.song.view.select_device(current_rack)
                    if hasattr(current_rack.view, 'is_collapsed'):
                        current_rack.view.is_collapsed = False

                # Stocker les informations du dernier appel
                self.last_call_type = 'select_rack_chain'
                self.last_selected_chain = final_chain
                self.last_selected_device = None
                self.last_call_params = params.copy()

            finally:
                # Programmer la réinitialisation du flag après un court délai
                self.manager.schedule_task(0.2, lambda: setattr(self.manager, 'is_selecting_rack_chain', False))

        except Exception as e:
            self.logger.error(f"Erreur dans select_rack_chain: {str(e)}")
            import traceback
            self.logger.error(traceback.format_exc())

    def select_chain_device(self, params):
        """
        Sélectionne un device en suivant un chemin de navigation avec plusieurs niveaux possibles.
        params: [rack_index, chain_index, (rack_index, chain_index)..., device_index]
        """
        try:
            if not params:
                self.logger.warning("Aucun paramètre reçu")
                return

            self.logger.info(f"Chemin reçu: {params}")

            selected_track = self.song.view.selected_track
            if not selected_track:
                self.logger.warning("Pas de piste sélectionnée")
                return

            self.manager.is_selecting_rack_chain = False
            current_device = selected_track.view.selected_device

            # Initialisation
            current_container = selected_track.devices
            final_device = None
            final_chain = None

            # Pour chaque paire d'indices sauf le dernier
            for i in range(0, len(params) - 1, 2):
                rack_index = int(params[i])
                chain_index = int(params[i + 1])

                if rack_index >= len(current_container):
                    self.logger.warning(f"Index de rack invalide: {rack_index}")
                    return

                rack = current_container[rack_index]

                if not hasattr(rack, 'chains'):
                    self.logger.warning(f"Le device {rack.name} n'est pas un rack")
                    return

                if chain_index >= len(rack.chains):
                    self.logger.warning(f"Index de chaîne invalide: {chain_index}")
                    return

                chain = rack.chains[chain_index]
                final_chain = chain

                # Sélection de la chaîne
                rack.view.selected_chain = chain

                # Déplier le rack
                if hasattr(rack.view, 'is_collapsed'):
                    rack.view.is_collapsed = False

                current_container = chain.devices

            # Sélection du device final
            final_device_index = int(params[-1])
            if final_device_index >= len(current_container):
                self.logger.warning(f"Index de device final invalide: {final_device_index}")
                return

            final_device = current_container[final_device_index]
            self.logger.info(f"Device qu'on va sélectionner: {final_device.name}")

            # Stocker les informations du dernier appel
            self.last_call_type = 'select_chain_device'
            self.last_selected_chain = final_chain
            self.last_selected_device = final_device
            self.last_call_params = params.copy()

            # Si c'est le même device, forcer la mise à jour
            if final_device == current_device:
                self.start_selected_device_listeners()
            else:
                # Sinon, laisser on_selected_track_or_device_changed s'en occuper
                self.song.view.select_device(final_device)

        except Exception as e:
            self.logger.error(f"Erreur dans select_chain_device: {str(e)}")
            import traceback
            self.logger.error(traceback.format_exc())

    def select_chain_by_note(self, params):
        try:
            if len(params) < 2:
                self.logger.warning("Il faut au minimum deux paramètres: rack_index et note_midi")
                return

            # Le dernier paramètre est la note MIDI
            note_midi = int(params[-1])
            path_indices = params[:-1]

            selected_track = self.song.view.selected_track
            if not selected_track:
                return

            self.logger.info(f"=== DÉBUT select_chain_by_note ===")
            self.logger.info(f"Note MIDI: {note_midi}")
            self.logger.info(f"Path indices: {path_indices}")

            # Navigation jusqu'au DrumRack
            current_container = selected_track.devices
            target_rack = None
            final_chain = None

            # Pour chaque paire d'indices (sauf le dernier qui est la note)
            for i in range(0, len(path_indices), 2):
                device_index = int(path_indices[i])

                if device_index >= len(current_container):
                    self.logger.warning(f"Index de device invalide: {device_index}")
                    return

                device = current_container[device_index]

                # Si on a un indice de chaîne suivant
                if i + 1 < len(path_indices):
                    chain_index = int(path_indices[i + 1])
                    if not hasattr(device, 'chains'):
                        self.logger.warning(f"Le device {device.name} n'est pas un rack")
                        return

                    if chain_index >= len(device.chains):
                        self.logger.warning(f"Index de chaîne invalide: {chain_index}")
                        return

                    current_container = device.chains[chain_index].devices
                else:
                    # C'est notre DrumRack cible
                    if not hasattr(device, 'can_have_drum_pads') or not device.can_have_drum_pads:
                        self.logger.warning(f"Le device {device.name} n'est pas un DrumRack")
                        return
                    target_rack = device

            if not target_rack:
                self.logger.warning("Pas de DrumRack trouvé")
                return

            # Cherche le pad correspondant à la note
            for pad in target_rack.drum_pads:
                if pad.note == note_midi:
                    self.logger.info(f"Pad trouvé pour la note {note_midi}")

                    # Important: on définit le flag AVANT de faire les sélections
                    self.manager.is_selecting_rack_chain = True

                    try:
                        # Si le pad a des chaînes, on sélectionne la première
                        if pad.chains:
                            final_chain = pad.chains[0]
                            target_rack.view.selected_chain = final_chain
                            self.logger.info(f"Chaîne sélectionnée pour le pad {note_midi}")

                        # Déplier le rack et le sélectionner
                        if hasattr(target_rack.view, 'is_collapsed'):
                            target_rack.view.is_collapsed = False
                        self.song.view.select_device(target_rack)

                        # Configuration des listeners avec le chemin complet
                        if final_chain:
                            # Construire le chemin complet incluant la note
                            complete_path = list(path_indices) + [note_midi]
                            self._setup_chain_listeners(final_chain, complete_path)

                    finally:
                        # Programmer la réinitialisation du flag après un court délai
                        self.manager.schedule_task(0.2, lambda: setattr(self.manager, 'is_selecting_rack_chain', False))
                    return

            self.logger.warning(f"Aucun pad trouvé pour la note {note_midi}")

        except Exception as e:
            self.logger.error(f"Erreur dans select_chain_by_note: {str(e)}")
            import traceback
            self.logger.error(traceback.format_exc())

    def cleanup(self):
        """
        Méthode à appeler lors de la fermeture du plugin pour nettoyer tous les listeners
        """
        self.logger.info("=== DÉBUT de cleanup pour DeviceMode ===")

        # Arrêter tous les listeners
        self._stop_device_listeners(self.deviceMode_listeners)
        self._stop_device_listeners(self.deviceLockMode_listeners)
        self._stop_chain_listeners()

        # Réinitialiser les variables
        self.navigation_path = []
        self.last_environment_data = None
        self.current_rack = None
        self.manager.lastSelectedDevice = None
        self.manager.lastLockedDevice = None

        # Réinitialiser les variables de dernier appel
        self.last_call_type = None
        self.last_selected_chain = None
        self.last_selected_device = None
        self.last_call_params = None

        # Nettoyer les buffers des paramètres quantifiés
        self.quantized_param_buffers.clear()

        self.logger.info("=== FIN de cleanup pour DeviceMode ===")

    # === MÉTHODES POUR LA GESTION DES ENCODERS ===

    def adjust_device_parameter(self, params):
        """
        Ajuste un paramètre de device en appliquant un delta à la valeur actuelle
        Détecte automatiquement si le paramètre est quantifié et applique la logique appropriée
        params: [parameter_index, delta_value]
        """
        try:
            if not params or len(params) != 2:
                self.logger.warning(f"Format de paramètres incorrect pour adjust_device_parameter: {params}")
                return

            parameter_index = int(params[0])
            delta_value = float(params[1])

            self.logger.debug(f"adjust_device_parameter: param_index={parameter_index}, delta={delta_value}")

            # Vérifier si on est en mode lock device
            if self.manager.deviceLock and self.manager.lockedDevice:
                device = self.manager.lockedDevice
                self.logger.debug(f"Mode lock device actif, utilisation du device verrouillé: {device.name}")
            else:
                # Utiliser le device sélectionné
                selected_track = self.song.view.selected_track
                if not selected_track or not selected_track.view.selected_device:
                    self.logger.warning("Pas de device sélectionné")
                    return
                device = selected_track.view.selected_device
                self.logger.debug(f"Utilisation du device sélectionné: {device.name}")

            # Vérifier que l'index du paramètre est valide
            if not hasattr(device, 'parameters') or parameter_index >= len(device.parameters):
                self.logger.warning(f"Index de paramètre invalide: {parameter_index} (max: {len(device.parameters)-1 if hasattr(device, 'parameters') else 'N/A'})")
                return

            parameter = device.parameters[parameter_index]
            current_value = parameter.value

            # Détecter si le device est un rack
            is_rack = self._is_device_rack(device)

            # Détecter si le paramètre est quantifié
            is_quantized = self._is_parameter_quantized(parameter)

            if is_quantized:
                # Pour les paramètres quantifiés, utiliser un système d'accumulation
                self.logger.debug(f"Paramètre {parameter_index} détecté comme quantifié (rack: {is_rack})")

                # Créer un ID unique pour ce paramètre
                param_id = f"{device.name}_{parameter_index}"

                # Récupérer le buffer actuel pour ce paramètre
                current_buffer = self.quantized_param_buffers.get(param_id, 0.0)

                # Ajouter le delta au buffer
                current_buffer += delta_value

                # Seuil pour déclencher un changement (plus élevé pour voir les valeurs intermédiaires)
                threshold = 0.5 # 20x plus élevé que le seuil normal (0.005)

                if abs(current_buffer) >= threshold:
                    # Calculer la direction du changement
                    direction = 1 if current_buffer > 0 else -1

                    # Incrémenter ou décrémenter par 1, en respectant les bornes
                    new_value = current_value + direction

                    # Bloquer aux extrémités (pas de comportement circulaire)
                    if new_value > parameter.max:
                        new_value = parameter.max
                        # Vider le buffer si on atteint l'extrémité
                        self.quantized_param_buffers[param_id] = 0.0
                    elif new_value < parameter.min:
                        new_value = parameter.min
                        # Vider le buffer si on atteint l'extrémité
                        self.quantized_param_buffers[param_id] = 0.0
                    else:
                        # Réduire le buffer du montant utilisé
                        self.quantized_param_buffers[param_id] = current_buffer - (direction * threshold)

                    self.logger.debug(f"Paramètre quantifié {parameter_index}: buffer={current_buffer:.3f}, threshold={threshold}, direction={direction}")
                else:
                    # Pas assez d'accumulation, garder la valeur actuelle et stocker le buffer
                    new_value = current_value
                    self.quantized_param_buffers[param_id] = current_buffer
                    self.logger.debug(f"Paramètre quantifié {parameter_index}: buffer={current_buffer:.3f} (en dessous du seuil)")
                    return  # Ne pas appliquer de changement
            else:
                # Pour les paramètres continus, ajuster le scaling selon le type de device
                self.logger.debug(f"Paramètre {parameter_index} détecté comme continu (rack: {is_rack})")

                if is_rack:
                    # Pour les racks, les paramètres vont de 0 à 127
                    # Un tour complet doit parcourir toute la plage, comme pour 0-1
                    # Si 0-1 utilise /5.0, alors 0-127 doit utiliser /5.0 * 127 = *25.4
                    scaled_delta = delta_value * 25.4  # Scaling pour que 1 tour = plage complète
                else:
                    # Pour les devices normaux, paramètres 0-1, scaling comme pour le volume
                    scaled_delta = delta_value / 5.0

                new_value = current_value + scaled_delta
                new_value = max(parameter.min, min(parameter.max, new_value))

            # Appliquer la nouvelle valeur
            parameter.value = new_value

            self.logger.debug(f"Paramètre {parameter_index} ajusté: {current_value} -> {new_value} (delta: {delta_value}, quantifié: {is_quantized}, rack: {is_rack}, plage: {parameter.min}-{parameter.max})")

        except Exception as e:
            self.logger.error(f"Erreur dans adjust_device_parameter: {str(e)}")
            import traceback
            self.logger.error(traceback.format_exc())

    def _is_device_rack(self, device):
        """
        Détermine si un device est un rack
        """
        try:
            # Vérifier si le device peut avoir des chaînes
            if hasattr(device, 'can_have_chains') and device.can_have_chains:
                return True

            # Vérifier si le device peut avoir des drum pads (DrumRack)
            if hasattr(device, 'can_have_drum_pads') and device.can_have_drum_pads:
                return True

            return False

        except Exception as e:
            self.logger.error(f"Erreur lors de la détection du type de device: {str(e)}")
            return False

    def _is_parameter_quantized(self, parameter):
        """
        Détermine si un paramètre est quantifié (discret) ou continu
        """
        try:
            # Vérifier si le paramètre a une propriété is_quantized
            if hasattr(parameter, 'is_quantized'):
                return parameter.is_quantized

            # Vérifier si le paramètre a des value_items (liste de valeurs discrètes)
            if hasattr(parameter, 'value_items') and parameter.value_items:
                return True

            # Heuristique basée sur la plage de valeurs
            value_range = parameter.max - parameter.min

            # Si la plage est très petite (0-1 ou similaire), c'est probablement quantifié
            if value_range <= 1.0:
                return True

            # Si la plage correspond à des valeurs MIDI typiques (0-127), c'est probablement quantifié
            if abs(value_range - 127.0) < 0.1:
                return True

            # Par défaut, considérer comme continu
            return False

        except Exception as e:
            self.logger.error(f"Erreur lors de la détection du type de paramètre: {str(e)}")
            return False



    def adjust_chain_parameter(self, params):
        """
        Ajuste un paramètre de chain (volume, pan, mute, solo)
        params: [path_json, parameter_type, delta_or_direction]
        parameter_type: 0=volume, 1=pan, 2=mute, 3=solo
        """
        try:
            if not params or len(params) != 3:
                self.logger.warning(f"Format de paramètres incorrect pour adjust_chain_parameter: {params}")
                return

            path_json = params[0]
            parameter_type = int(params[1])
            value = float(params[2])

            self.logger.debug(f"adjust_chain_parameter: path={path_json}, type={parameter_type}, value={value}")

            # Trouver la chain à partir du chemin
            target_chain = self._find_chain_by_path(path_json)
            if not target_chain:
                self.logger.warning("Chain non trouvée pour le chemin spécifié")
                return

            if not hasattr(target_chain, 'mixer_device'):
                self.logger.warning("Chain sans mixer_device")
                return

            mixer = target_chain.mixer_device

            if parameter_type == 0:  # Volume
                if hasattr(mixer, 'volume'):
                    current_value = mixer.volume.value
                    # Appliquer le même scaling que pour le volume des tracks
                    scaled_delta = value / 5.0
                    new_value = current_value + scaled_delta
                    new_value = max(mixer.volume.min, min(mixer.volume.max, new_value))
                    mixer.volume.value = new_value
                    self.logger.debug(f"Volume chain ajusté: {current_value} -> {new_value} (delta: {value}, scaled: {scaled_delta})")

            elif parameter_type == 1:  # Pan
                if hasattr(mixer, 'panning'):
                    current_value = mixer.panning.value
                    # Appliquer le même scaling que pour le pan des tracks
                    scaled_delta = value / 5.0
                    new_value = current_value + scaled_delta
                    new_value = max(mixer.panning.min, min(mixer.panning.max, new_value))
                    mixer.panning.value = new_value
                    self.logger.debug(f"Pan chain ajusté: {current_value} -> {new_value} (delta: {value}, scaled: {scaled_delta})")

            elif parameter_type == 2:  # Mute (toggle)
                # Les propriétés mute et solo sont directement sur la chain, pas sur le mixer
                if hasattr(target_chain, 'mute'):
                    current_mute = target_chain.mute
                    target_chain.mute = not current_mute
                    self.logger.debug(f"Mute chain togglé: {current_mute} -> {target_chain.mute}")
                else:
                    self.logger.warning("Pas de propriété mute trouvée sur la chain")

            elif parameter_type == 3:  # Solo (toggle)
                # Les propriétés mute et solo sont directement sur la chain, pas sur le mixer
                if hasattr(target_chain, 'solo'):
                    current_solo = target_chain.solo
                    target_chain.solo = not current_solo
                    self.logger.debug(f"Solo chain togglé: {current_solo} -> {target_chain.solo}")
                else:
                    self.logger.warning("Pas de propriété solo trouvée sur la chain")

            else:
                self.logger.warning(f"Type de paramètre chain non supporté: {parameter_type}")

        except Exception as e:
            self.logger.error(f"Erreur dans adjust_chain_parameter: {str(e)}")
            import traceback
            self.logger.error(traceback.format_exc())

    def _find_chain_by_path(self, path_indices):
        """
        Helper pour trouver une chaîne à partir d'un chemin d'indices.
        Args:
            path_indices: Liste d'indices [rack_index, chain_index, ...] ou chaîne JSON
        Returns:
            target_chain ou None si non trouvé
        """
        try:
            self.logger.info(f"=== DÉBUT _find_chain_by_path avec path_indices: {path_indices} ===")

            # Traitement du chemin qui peut être une chaîne JSON ou une liste
            if isinstance(path_indices, (bytes, bytearray)):
                path_indices = path_indices.decode('utf-8')

            if isinstance(path_indices, str):
                try:
                    # Tenter de parser comme JSON
                    path_indices = json.loads(path_indices)
                    self.logger.info(f"Chemin JSON parsé: {path_indices}")
                except json.JSONDecodeError as e:
                    self.logger.error(f"Erreur de décodage JSON: {e}")
                    return None

            if not path_indices or len(path_indices) < 2:
                self.logger.warning("Chemin trop court pour trouver une chaîne")
                return None

            selected_track = self.song.view.selected_track
            if not selected_track:
                self.logger.warning("Pas de piste sélectionnée")
                return None

            self.logger.info(f"Piste sélectionnée: {selected_track.name}")
            current_container = selected_track.devices
            self.logger.info(f"Nombre de devices dans le container initial: {len(current_container)}")
            target_chain = None

            for i in range(0, len(path_indices), 2):
                rack_index = int(path_indices[i])
                chain_index = int(path_indices[i + 1])
                self.logger.info(f"Recherche rack[{rack_index}] -> chain[{chain_index}]")

                if rack_index >= len(current_container):
                    self.logger.warning(f"Index de rack invalide: {rack_index} (max: {len(current_container)-1})")
                    return None

                rack = current_container[rack_index]
                self.logger.info(f"Rack trouvé: {rack.name}")

                if not hasattr(rack, 'chains'):
                    self.logger.warning(f"Le device {rack.name} n'est pas un rack")
                    return None

                self.logger.info(f"Nombre de chaînes dans le rack: {len(rack.chains)}")

                if chain_index >= len(rack.chains):
                    self.logger.warning(f"Index de chaîne invalide: {chain_index} (max: {len(rack.chains)-1})")
                    return None

                chain = rack.chains[chain_index]
                target_chain = chain
                current_container = chain.devices
                self.logger.info(f"Chaîne trouvée, nombre de devices: {len(current_container)}")

            self.logger.info(f"=== FIN _find_chain_by_path : chaîne trouvée ===")
            return target_chain

        except Exception as e:
            self.logger.error(f"Erreur dans _find_chain_by_path: {str(e)}")
            self.logger.error(traceback.format_exc())
            return None

    def set_chain_volume_by_path(self, params):
        """
        Définit le volume d'une chaîne spécifiée par son chemin.
        params: [ [path_indices], volume_value ] ou [ "JSON_string", volume_value ]
        """
        try:
            self.logger.info(f"=== DÉBUT set_chain_volume_by_path avec params: {params} ===")

            if not params or len(params) != 2:
                self.logger.warning(f"Format de paramètres incorrect pour set_chain_volume_by_path: {params}")
                return

            path_indices = params[0]  # Premier élément : la liste des indices [rack_index, chain_index] ou JSON
            volume_value = float(params[1])  # Deuxième élément : la valeur de volume

            self.logger.info(f"Recherche de la chaîne avec path_indices: {path_indices}")
            self.logger.info(f"Valeur de volume à appliquer: {volume_value}")

            target_chain = self._find_chain_by_path(path_indices)
            if target_chain:
                self.logger.info("Chaîne trouvée, vérification du mixer_device")
                if hasattr(target_chain, 'mixer_device'):
                    self.logger.info("mixer_device trouvé, vérification du volume")
                    mixer = target_chain.mixer_device
                    if hasattr(mixer, 'volume'):
                        volume = mixer.volume
                        self.logger.info(f"Contrôle de volume trouvé, valeur actuelle: {volume.value}")
                        self.logger.info(f"Tentative d'application du volume {volume_value}")

                        # Vérifions les limites du volume
                        self.logger.info(f"Limites du volume - Min: {volume.min}, Max: {volume.max}")

                        # Appliquons le volume et vérifions qu'il a bien été appliqué
                        volume.value = volume_value
                        self.logger.info(f"Nouvelle valeur du volume après modification: {volume.value}")
                    else:
                        self.logger.warning("Pas de contrôle de volume trouvé sur le mixer_device")
                else:
                    self.logger.warning("Pas de mixer_device trouvé sur la chaîne")
            else:
                self.logger.warning("Chaîne non trouvée")

            self.logger.info("=== FIN set_chain_volume_by_path ===")

        except Exception as e:
            self.logger.error(f"Erreur dans set_chain_volume_by_path: {str(e)}")
            self.logger.error(traceback.format_exc())

    def set_chain_pan_by_path(self, params):
        """
        Définit le panoramique d'une chaîne spécifiée par son chemin.
        params: [ [path_indices], pan_value ] ou [ "JSON_string", pan_value ]
        """
        try:
            self.logger.info(f"=== DÉBUT set_chain_pan_by_path avec params: {params} ===")

            if not params or len(params) != 2:
                self.logger.warning("Format de paramètres incorrect pour set_chain_pan_by_path")
                return

            path_indices = params[0]  # Premier élément : la liste des indices [rack_index, chain_index] ou JSON
            pan_value = float(params[1])  # Deuxième élément : la valeur de pan

            self.logger.info(f"Recherche de la chaîne avec path_indices: {path_indices}")
            self.logger.info(f"Valeur de pan à appliquer: {pan_value}")

            target_chain = self._find_chain_by_path(path_indices)
            if target_chain:
                self.logger.info("Chaîne trouvée, vérification du mixer_device")
                if hasattr(target_chain, 'mixer_device') and hasattr(target_chain.mixer_device, 'panning'):
                    self.logger.info(f"Contrôle de panning trouvé, valeur actuelle: {target_chain.mixer_device.panning.value}")
                    target_chain.mixer_device.panning.value = pan_value
                    self.logger.info(f"Nouvelle valeur du panning après modification: {target_chain.mixer_device.panning.value}")
                else:
                    self.logger.warning("Pas de contrôle de panning trouvé sur le mixer_device")
            else:
                self.logger.warning("Chaîne non trouvée")

            self.logger.info("=== FIN set_chain_pan_by_path ===")

        except Exception as e:
            self.logger.error(f"Erreur dans set_chain_pan_by_path: {str(e)}")
            self.logger.error(traceback.format_exc())

    def toggle_chain_mute_by_path(self, params):
        """
        Bascule l'état mute d'une chaîne spécifiée par son chemin.
        params: [ [path_indices] ] ou [ "JSON_string" ]
        """
        try:
            self.logger.info(f"=== DÉBUT toggle_chain_mute_by_path avec params: {params} ===")

            if not params:
                self.logger.warning(f"Aucun paramètre reçu pour toggle_chain_mute_by_path")
                return

            # Extraction du chemin (peut être un JSON string ou une liste)
            path_indices = params[0]

            self.logger.info(f"Recherche de la chaîne avec path_indices: {path_indices}")

            target_chain = self._find_chain_by_path(path_indices)
            if target_chain:
                self.logger.info("Chaîne trouvée, vérification de la propriété mute")
                if hasattr(target_chain, 'mute'):
                    current_state = target_chain.mute
                    self.logger.info(f"État actuel du mute: {current_state}")
                    target_chain.mute = not current_state
                    self.logger.info(f"Nouvel état du mute: {target_chain.mute}")
                else:
                    self.logger.warning("Pas de propriété mute trouvée sur la chaîne")
            else:
                self.logger.warning("Chaîne non trouvée")

            self.logger.info("=== FIN toggle_chain_mute_by_path ===")

        except Exception as e:
            self.logger.error(f"Erreur dans toggle_chain_mute_by_path: {str(e)}")
            self.logger.error(traceback.format_exc())

    def toggle_chain_solo_by_path(self, params):
        """
        Bascule l'état solo d'une chaîne spécifiée par son chemin.
        params: [ [path_indices] ] ou [ "JSON_string" ]
        """
        try:
            self.logger.info(f"=== DÉBUT toggle_chain_solo_by_path avec params: {params} ===")

            if not params:
                self.logger.warning(f"Aucun paramètre reçu pour toggle_chain_solo_by_path")
                return

            # Extraction du chemin (peut être un JSON string ou une liste)
            path_indices = params[0]

            self.logger.info(f"Recherche de la chaîne avec path_indices: {path_indices}")

            target_chain = self._find_chain_by_path(path_indices)
            if target_chain:
                self.logger.info("Chaîne trouvée, vérification de la propriété solo")
                if hasattr(target_chain, 'solo'):
                    current_state = target_chain.solo
                    self.logger.info(f"État actuel du solo: {current_state}")
                    target_chain.solo = not current_state
                    self.logger.info(f"Nouvel état du solo: {target_chain.solo}")
                else:
                    self.logger.warning("Pas de propriété solo trouvée sur la chaîne")
            else:
                self.logger.warning("Chaîne non trouvée")

            self.logger.info("=== FIN toggle_chain_solo_by_path ===")

        except Exception as e:
            self.logger.error(f"Erreur dans toggle_chain_solo_by_path: {str(e)}")
            self.logger.error(traceback.format_exc())


