package livedevicemode

import (
	communication "oscbridge/Communication"
	"time"
)

// Constantes pour les sous-modes
const (
	SubmodeDevice = 1 // Submode device (valeur impaire du path)
	SubmodeChain  = 0 // Submode chain (valeur paire du path)
)

// Constantes pour la pagination
const (
	SlotsPerPage = 8 // Nombre de slots par page
)

// LiveDeviceModeState représente l'état du mode device
type LiveDeviceModeState struct {
	IsActive     bool
	IsLocked     bool
	LockedDevice *int
	LockedTrack  *int
	SubMode      int // 0 = chain, 1 = device
	CurrentPage  int
	Path         []int
	IsDrumRack   bool
	BaseMode     *communication.BaseMode
	PathString   string // Chemin actuel sous forme de chaîne
}

// DeviceParameterInfo contient les informations d'un paramètre de device
type DeviceParameterInfo struct {
	Name          string  // Nom du paramètre
	CurrentValue  float64 // Valeur actuelle
	MinValue      float64 // Valeur minimum
	MaxValue      float64 // Valeur maximum
	IsQuantized   bool    // Si le paramètre est quantifié
	Buffer        float64 // Buffer pour les changements de valeur
	LastDirection int     // Dernière direction de changement
}

// ChainParameterInfo contient les informations d'un paramètre de chaîne
type ChainParameterInfo struct {
	Volume float64
	Pan    float64
	Mute   bool
	Solo   bool
}

// PageChangeState gère l'état des changements de page
type PageChangeState struct {
	LastChange time.Time
	Debounce   time.Duration
}

// NewPageChangeState crée un nouvel état de changement de page
func NewPageChangeState() *PageChangeState {
	return &PageChangeState{
		LastChange: time.Now(),
		Debounce:   time.Millisecond * 200, // 200ms de debounce
	}
}
