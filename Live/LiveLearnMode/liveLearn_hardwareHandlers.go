package livelearnmode

import (
	"fmt"
	"log"
	communication "oscbridge/Communication"
	live "oscbridge/Live"
	"oscbridge/Live/types"
)

// HandleHardwareEvent gère les événements hardware (encodeurs, boutons, etc.)
func (m *LiveLearnMode) HandleHardwareEvent(event communication.HardwareEvent) {
	// Vérifier si le mode est actif
	if !m.isActive {
		return
	}

	log.Printf("Événement hardware reçu en mode learn: %s", event.Type)

	switch event.Type {
	case "encoder":
		// Gérer les changements d'encodeurs (rotation)
		if event.EncoderEvent != nil {
			m.handleEncoderChange(event.EncoderEvent.Index, event.EncoderEvent.Value)
		}
	case "button":
		// Gérer les événements de boutons avec état
		m.handleButtonEvent(event.ButtonEvent)
	case "touch":
		// Gérer les pressions sur l'écran tactile
		if event.TouchEvent != nil {
			m.handleTouchPressed(event.TouchEvent.TouchType, event.TouchEvent.Index)
		}
	}
}

// handleEncoderChange gère les changements d'encodeurs
func (m *LiveLearnMode) handleEncoderChange(encoderIndex int, direction int) {
	// Vérifier si l'encodeur est dans la plage valide (0-7)
	if encoderIndex < 0 || encoderIndex >= SlotsPerPage {
		return
	}

	// Calculer l'index absolu du slot basé sur la page courante
	slotIndex := (m.state.CurrentPage-1)*SlotsPerPage + encoderIndex

	// Récupérer les informations du slot
	m.state.Mutex.RLock()
	slot, exists := m.state.Slots[slotIndex]
	m.state.Mutex.RUnlock()

	if !exists || slot.Type == nil {
		log.Printf("Encodeur %d n'a pas de paramètre associé sur cette page", encoderIndex)
		return
	}

	// Traiter différemment selon le type de paramètre
	switch *slot.Type {
	case types.ParamTypeMute, types.ParamTypeSolo, types.ParamTypeChainMute, types.ParamTypeChainSolo:
		// Pour mute et solo, utiliser une logique binaire simple (on/off)
		newValue := float64(0)
		if direction > 0 {
			newValue = 1.0
		}

		if slot.Value == nil || *slot.Value != newValue {
			// Mettre à jour la valeur
			value := newValue
			slot.Value = &value

			// Envoyer la mise à jour
			m.sendParameterUpdate(slotIndex, *slot.Type, -3, newValue)
			log.Printf("Encodeur %d (slot %d) valeur changée à %v", encoderIndex, slotIndex, newValue)
		}

	case types.ParamTypeSend:
		// Pour les sends, utiliser l'index de send
		var indexBis int = -3
		if slot.SendIndex != nil {
			indexBis = *slot.SendIndex
		}

		// Calculer la nouvelle valeur
		newValue := m.updateParameterValue(slot, direction)

		// Envoyer la mise à jour
		m.sendParameterUpdate(slotIndex, *slot.Type, indexBis, newValue)

	case types.ParamTypeDevice:
		// Pour les paramètres de device, utiliser l'index de paramètre
		var indexBis int = -3
		if slot.ParameterIndex != nil {
			indexBis = *slot.ParameterIndex
		}

		// Calculer la nouvelle valeur
		newValue := m.updateParameterValue(slot, direction)

		// Envoyer la mise à jour
		m.sendParameterUpdate(slotIndex, *slot.Type, indexBis, newValue)

	default:
		// Pour les autres types (volume, pan, etc.)
		// Calculer la nouvelle valeur
		newValue := m.updateParameterValue(slot, direction)

		// Envoyer la mise à jour
		m.sendParameterUpdate(slotIndex, *slot.Type, -3, newValue)
	}

	// Mettre à jour le slot dans l'état
	m.state.Mutex.Lock()
	m.state.Slots[slotIndex] = slot
	m.state.Mutex.Unlock()
}

// updateParameterValue calcule la nouvelle valeur d'un paramètre en fonction de la direction
func (m *LiveLearnMode) updateParameterValue(slot *SlotInfo, direction int) float64 {
	// Si le slot n'a pas de valeur, retourner 0
	if slot.Value == nil {
		return 0
	}

	currentValue := *slot.Value
	var newValue float64

	// Si le paramètre est quantifié (valeurs discrètes)
	if slot.IsQuantized != nil && *slot.IsQuantized {
		// Gérer le buffer pour les paramètres quantifiés
		// Utiliser une variable temporaire pour le buffer car slot.Buffer est un int
		bufferValue := float64(slot.Buffer)

		// Accumuler les changements dans le buffer
		bufferStep := 0.25
		bufferValue += float64(direction) * bufferStep

		// Si le buffer atteint un seuil, changer la valeur
		if bufferValue >= 1 || bufferValue <= -1 {
			change := 1.0
			if bufferValue < 0 {
				change = -1.0
			}

			// Calculer la nouvelle valeur en respectant les limites
			if slot.Min != nil && slot.Max != nil {
				newValue = currentValue + change
				if newValue < *slot.Min {
					newValue = *slot.Min
				} else if newValue > *slot.Max {
					newValue = *slot.Max
				}
			} else {
				// Valeurs par défaut si min/max ne sont pas définis
				newValue = currentValue + change
				if newValue < 0 {
					newValue = 0
				} else if newValue > 1 {
					newValue = 1
				}
			}

			// Réduire le buffer
			bufferValue -= change
			// Mettre à jour le buffer dans le slot (conversion en int)
			slot.Buffer = int(bufferValue)
		} else {
			// Pas assez de changement accumulé, garder la valeur actuelle
			newValue = currentValue
		}
	} else {
		// Pour les paramètres continus
		// Calculer le pas minimum et maximum
		maxStep := 0.025

		if slot.Min != nil && slot.Max != nil {
			// Adapter les pas à la plage du paramètre
			maxStep = (*slot.Max - *slot.Min) / 40
		}

		// Calculer le pas en fonction de la direction
		var step float64
		if direction > 0 {
			step = maxStep
		} else {
			step = -maxStep
		}

		// Calculer la nouvelle valeur
		newValue = currentValue + step

		// Limiter la valeur à la plage min-max
		if slot.Min != nil && newValue < *slot.Min {
			newValue = *slot.Min
		}
		if slot.Max != nil && newValue > *slot.Max {
			newValue = *slot.Max
		}
	}

	// Mettre à jour la valeur dans le slot
	value := newValue
	slot.Value = &value

	return newValue
}

// sendParameterUpdate envoie une mise à jour de paramètre à Live
func (m *LiveLearnMode) sendParameterUpdate(slotIndex, paramType, indexBis int, newValue float64) {
	// Envoyer le message OSC pour mettre à jour la valeur
	m.BaseMode.Send("/live/learn/set/value", []interface{}{slotIndex, paramType, indexBis, newValue})

	// Pour les paramètres de chaîne, envoyer également un message spécifique
	if paramType == types.ParamTypeChainVolume || paramType == types.ParamTypeChainPan {
		slot := m.state.Slots[slotIndex]
		if slot != nil && len(slot.ChainPath) > 0 {
			// Déterminer le type de message
			messageType := "volume"
			if paramType == types.ParamTypeChainPan {
				messageType = "panning"
			}

			// Envoyer le message spécifique à la chaîne
			m.BaseMode.Send("/live/chain/set/"+messageType, []interface{}{slot.ChainPath, newValue})
		}
	}
}

// handleButtonPressed gère les pressions de boutons
func (m *LiveLearnMode) handleButtonPressed(buttonIndex int) {
	// Vérifier si le bouton est dans la plage valide (0-7)
	if buttonIndex < 0 || buttonIndex >= SlotsPerPage {
		return
	}

	// Calculer l'index absolu du slot basé sur la page courante
	slotIndex := (m.state.CurrentPage-1)*SlotsPerPage + buttonIndex

	// Démarrer l'apprentissage pour ce slot
	m.StartLearning(slotIndex)
}

// handleButtonEvent traite les événements de bouton avec gestion d'état
func (m *LiveLearnMode) handleButtonEvent(event *communication.ButtonEvent) {
	if event == nil {
		return
	}

	log.Printf("Mode Learn: Bouton %d état %d", event.Index, event.State)

	// Filtrer les événements : ne traiter que les appuis (état 1)
	if event.State != 1 {
		log.Printf("Mode Learn: Bouton %d relâché, ignoré", event.Index)
		return
	}

	log.Printf("Mode Learn: Bouton %d appuyé, traitement", event.Index)

	switch event.Index {
	case 0: // B00 - Go to page 1
		log.Println("Mode Learn: B00 - Go to page 1")
		m.SetPage(1)

	case 1: // B01 - Track Mode
		log.Println("Mode Learn: B01 - Switch to Track Mode")
		m.BaseMode.Emit("modeChange", "track")

	case 2: // B02 - Main Mode (Volume)
		log.Println("Mode Learn: B02 - Switch to Volume Mode")
		m.BaseMode.Emit("modeChange", "volume")

	case 3: // B03 - Device Mode
		log.Println("Mode Learn: B03 - Switch to Device Mode")
		m.BaseMode.Emit("modeChange", "device")

	case 4: // B04 - Browse Mode
		log.Println("Mode Learn: B04 - Switch to Browse Mode")
		m.BaseMode.Emit("modeChange", "browser")

	// B05 est maintenant géré comme bouton transversal dans HardwareManager

	case 6: // B06 - (non défini)
		log.Printf("Mode Learn: B06 - Non défini")

	case 7: // B07 - (non défini)
		log.Printf("Mode Learn: B07 - Non défini")

	case 8: // B08 - Page down
		log.Println("Mode Learn: B08 - Page down")
		m.PageDown()

	case 9: // B09 - Page up
		log.Println("Mode Learn: B09 - Page up")
		m.PageUp()

	default:
		log.Printf("Mode Learn: Bouton %d non géré", event.Index)
	}
}

// handleTouchPressed gère les pressions sur l'écran tactile
func (m *LiveLearnMode) handleTouchPressed(touchType string, index int) {
	log.Printf("Touch pressed: type=%s, index=%d", touchType, index)

	switch touchType {
	case "lst":
		// Toucher sur un slot (pour démarrer l'apprentissage)
		slotIndex := index + SlotsPerPage*(m.state.CurrentPage-1)
		m.StartLearning(slotIndex)

	case "lw":
		// Toucher sur un slot pour le sélectionner (widget)
		// Calculer l'index absolu du slot basé sur la page courante
		absoluteIndex := index + SlotsPerPage*(m.state.CurrentPage-1)

		// Envoyer un message OSC pour sélectionner le slot
		log.Printf("Sélection du slot %d via message lw", absoluteIndex)
		m.BaseMode.Send("/live/learn/select", []interface{}{absoluteIndex})

	case "sm":
		// Toucher sur un bouton de mode (pour changer de mode)
		if m.state.IsLearning {
			m.StopLearning()
		}

		// Émettre un événement pour changer de mode
		switch index {
		case 0:
			m.BaseMode.Emit("modeChange", "track")
		case 1:
			m.BaseMode.Emit("modeChange", "volume")
		case 2:
			m.BaseMode.Emit("modeChange", "device")
		case 4:
			m.BaseMode.Emit("modeChange", "browser")
		}

	case "cl":
		// Toucher sur un slot pour le sélectionner ou démarrer l'apprentissage
		slotIndex := index + SlotsPerPage*(m.state.CurrentPage-1)

		// Récupérer les informations du slot
		m.state.Mutex.RLock()
		slot, exists := m.state.Slots[slotIndex]
		hasType := exists && slot.Type != nil
		m.state.Mutex.RUnlock()

		if !hasType {
			// Récupérer les données d'apprentissage du LiveModeManager
			var learnData *types.LearnData
			var modeManager *live.LiveModeManager

			if service := m.BaseMode.GetService(); service != nil {
				if mm, ok := service.(*live.LiveModeManager); ok {
					modeManager = mm
					learnData = mm.GetLearnData()
				}
			}

			// VÉRIFIER EN PREMIER si learnData contient quelque chose
			if learnData != nil {
				// Activer le mode apprentissage si ce n'est pas déjà fait
				if !m.state.IsLearning {
					m.state.Mutex.Lock()
					m.state.IsLearning = true
					m.state.ActiveSlot = &slotIndex
					m.state.Mutex.Unlock()

					// Mettre à jour l'affichage du statut d'apprentissage
					m.displayManager.UpdateLearningStatus(true, slotIndex)
					log.Printf("Mode apprentissage activé pour le slot %d", slotIndex)
				}

				log.Printf("Simulation de la réponse de Live pour le type de paramètre %d", learnData.ParamType)

				switch learnData.ParamType {
				case types.ParamTypeVolume:
					// Simuler un message de volume
					if learnData.TrackIndex >= 0 {
						// Forcer le slotIndex à être celui sur lequel l'utilisateur a cliqué
						m.state.Mutex.Lock()
						oldActiveSlot := m.state.ActiveSlot
						m.state.ActiveSlot = &slotIndex
						m.state.Mutex.Unlock()

						// Appeler le handler
						m.handleTrackLearningVolume([]interface{}{learnData.TrackIndex + 1, 0.0})

						// Restaurer l'ancien slot actif si nécessaire
						if oldActiveSlot != nil && *oldActiveSlot != slotIndex {
							m.state.Mutex.Lock()
							m.state.ActiveSlot = oldActiveSlot
							m.state.Mutex.Unlock()
						}
					}
				case types.ParamTypePan:
					// Simuler un message de panoramique
					if learnData.TrackIndex >= 0 {
						// Forcer le slotIndex à être celui sur lequel l'utilisateur a cliqué
						m.state.Mutex.Lock()
						oldActiveSlot := m.state.ActiveSlot
						m.state.ActiveSlot = &slotIndex
						m.state.Mutex.Unlock()

						// Appeler le handler
						m.handleTrackLearningPanning([]interface{}{learnData.TrackIndex + 1, 0.0})

						// Restaurer l'ancien slot actif si nécessaire
						if oldActiveSlot != nil && *oldActiveSlot != slotIndex {
							m.state.Mutex.Lock()
							m.state.ActiveSlot = oldActiveSlot
							m.state.Mutex.Unlock()
						}
					}
				case types.ParamTypeSend:
					// Simuler un message de send
					if learnData.TrackIndex >= 0 && learnData.SendIndex != nil {
						// Forcer le slotIndex à être celui sur lequel l'utilisateur a cliqué
						m.state.Mutex.Lock()
						oldActiveSlot := m.state.ActiveSlot
						m.state.ActiveSlot = &slotIndex
						m.state.Mutex.Unlock()

						// Appeler le handler
						m.handleTrackLearningSends([]interface{}{learnData.TrackIndex + 1, *learnData.SendIndex, 0.0})

						// Restaurer l'ancien slot actif si nécessaire
						if oldActiveSlot != nil && *oldActiveSlot != slotIndex {
							m.state.Mutex.Lock()
							m.state.ActiveSlot = oldActiveSlot
							m.state.Mutex.Unlock()
						}
					}
				case types.ParamTypeMute:
					// Simuler un message de mute
					if learnData.TrackIndex >= 0 {
						// Forcer le slotIndex à être celui sur lequel l'utilisateur a cliqué
						m.state.Mutex.Lock()
						oldActiveSlot := m.state.ActiveSlot
						m.state.ActiveSlot = &slotIndex
						m.state.Mutex.Unlock()

						// Appeler le handler
						m.handleTrackLearningMute([]interface{}{learnData.TrackIndex + 1, 0})

						// Restaurer l'ancien slot actif si nécessaire
						if oldActiveSlot != nil && *oldActiveSlot != slotIndex {
							m.state.Mutex.Lock()
							m.state.ActiveSlot = oldActiveSlot
							m.state.Mutex.Unlock()
						}
					}
				case types.ParamTypeSolo:
					// Simuler un message de solo
					if learnData.TrackIndex >= 0 {
						// Forcer le slotIndex à être celui sur lequel l'utilisateur a cliqué
						m.state.Mutex.Lock()
						oldActiveSlot := m.state.ActiveSlot
						m.state.ActiveSlot = &slotIndex
						m.state.Mutex.Unlock()

						// Appeler le handler
						m.handleTrackLearningSolo([]interface{}{learnData.TrackIndex + 1, 0})

						// Restaurer l'ancien slot actif si nécessaire
						if oldActiveSlot != nil && *oldActiveSlot != slotIndex {
							m.state.Mutex.Lock()
							m.state.ActiveSlot = oldActiveSlot
							m.state.Mutex.Unlock()
						}
					}
				case types.ParamTypeChainVolume:
					// Simuler un message de volume de chaîne
					if len(learnData.ChainPath) > 0 {
						// Forcer le slotIndex à être celui sur lequel l'utilisateur a cliqué
						m.state.Mutex.Lock()
						oldActiveSlot := m.state.ActiveSlot
						m.state.ActiveSlot = &slotIndex
						m.state.IsLearning = true
						m.state.Mutex.Unlock()

						// Mettre à jour l'affichage du statut d'apprentissage
						m.displayManager.UpdateLearningStatus(true, slotIndex)

						// Convertir le chemin de la chaîne en []interface{} pour l'envoyer
						chainPathInterface := make([]interface{}, len(learnData.ChainPath))
						for i, v := range learnData.ChainPath {
							chainPathInterface[i] = v
						}

						// Simuler la réponse pour le volume de la chaîne
						log.Printf("Simulation de la réponse pour le volume de la chaîne avec chemin: %v", chainPathInterface)
						m.handleChainLearningVolume([]interface{}{ChainTrackIndex, chainPathInterface, 0.0})

						// Restaurer l'ancien slot actif si nécessaire
						if oldActiveSlot != nil && *oldActiveSlot != slotIndex {
							m.state.Mutex.Lock()
							m.state.ActiveSlot = oldActiveSlot
							m.state.Mutex.Unlock()
						}

						// Désactiver le mode apprentissage
						m.state.Mutex.Lock()
						m.state.IsLearning = false
						m.state.Mutex.Unlock()
						m.displayManager.UpdateLearningStatus(false, 0)
					}
				case types.ParamTypeChainPan:
					// Simuler un message de panoramique de chaîne
					if len(learnData.ChainPath) > 0 {
						// Forcer le slotIndex à être celui sur lequel l'utilisateur a cliqué
						m.state.Mutex.Lock()
						oldActiveSlot := m.state.ActiveSlot
						m.state.ActiveSlot = &slotIndex
						m.state.IsLearning = true
						m.state.Mutex.Unlock()

						// Mettre à jour l'affichage du statut d'apprentissage
						m.displayManager.UpdateLearningStatus(true, slotIndex)

						// Convertir le chemin de la chaîne en []interface{} pour l'envoyer
						chainPathInterface := make([]interface{}, len(learnData.ChainPath))
						for i, v := range learnData.ChainPath {
							chainPathInterface[i] = v
						}

						// Simuler la réponse pour le pan de la chaîne
						log.Printf("Simulation de la réponse pour le pan de la chaîne avec chemin: %v", chainPathInterface)
						m.handleChainLearningPanning([]interface{}{ChainTrackIndex, chainPathInterface, 0.0})

						// Restaurer l'ancien slot actif si nécessaire
						if oldActiveSlot != nil && *oldActiveSlot != slotIndex {
							m.state.Mutex.Lock()
							m.state.ActiveSlot = oldActiveSlot
							m.state.Mutex.Unlock()
						}

						// Désactiver le mode apprentissage
						m.state.Mutex.Lock()
						m.state.IsLearning = false
						m.state.Mutex.Unlock()
						m.displayManager.UpdateLearningStatus(false, 0)
					}
				case types.ParamTypeChainMute:
					// Simuler un message de mute de chaîne
					if len(learnData.ChainPath) > 0 {
						// Forcer le slotIndex à être celui sur lequel l'utilisateur a cliqué
						m.state.Mutex.Lock()
						oldActiveSlot := m.state.ActiveSlot
						m.state.ActiveSlot = &slotIndex
						m.state.IsLearning = true
						m.state.Mutex.Unlock()

						// Mettre à jour l'affichage du statut d'apprentissage
						m.displayManager.UpdateLearningStatus(true, slotIndex)

						// Convertir le chemin de la chaîne en []interface{} pour l'envoyer
						chainPathInterface := make([]interface{}, len(learnData.ChainPath))
						for i, v := range learnData.ChainPath {
							chainPathInterface[i] = v
						}

						// Simuler la réponse pour le mute de la chaîne
						log.Printf("Simulation de la réponse pour le mute de la chaîne avec chemin: %v", chainPathInterface)
						m.handleChainLearningMute([]interface{}{ChainTrackIndex, chainPathInterface, 0})

						// Restaurer l'ancien slot actif si nécessaire
						if oldActiveSlot != nil && *oldActiveSlot != slotIndex {
							m.state.Mutex.Lock()
							m.state.ActiveSlot = oldActiveSlot
							m.state.Mutex.Unlock()
						}

						// Désactiver le mode apprentissage
						m.state.Mutex.Lock()
						m.state.IsLearning = false
						m.state.Mutex.Unlock()
						m.displayManager.UpdateLearningStatus(false, 0)
					}
				case types.ParamTypeChainSolo:
					// Simuler un message de solo de chaîne
					if len(learnData.ChainPath) > 0 {
						// Forcer le slotIndex à être celui sur lequel l'utilisateur a cliqué
						m.state.Mutex.Lock()
						oldActiveSlot := m.state.ActiveSlot
						m.state.ActiveSlot = &slotIndex
						m.state.IsLearning = true
						m.state.Mutex.Unlock()

						// Mettre à jour l'affichage du statut d'apprentissage
						m.displayManager.UpdateLearningStatus(true, slotIndex)

						// Convertir le chemin de la chaîne en []interface{} pour l'envoyer
						chainPathInterface := make([]interface{}, len(learnData.ChainPath))
						for i, v := range learnData.ChainPath {
							chainPathInterface[i] = v
						}

						// Simuler la réponse pour le solo de la chaîne
						log.Printf("Simulation de la réponse pour le solo de la chaîne avec chemin: %v", chainPathInterface)
						m.handleChainLearningSolo([]interface{}{ChainTrackIndex, chainPathInterface, 0})

						// Restaurer l'ancien slot actif si nécessaire
						if oldActiveSlot != nil && *oldActiveSlot != slotIndex {
							m.state.Mutex.Lock()
							m.state.ActiveSlot = oldActiveSlot
							m.state.Mutex.Unlock()
						}

						// Désactiver le mode apprentissage
						m.state.Mutex.Lock()
						m.state.IsLearning = false
						m.state.Mutex.Unlock()
						m.displayManager.UpdateLearningStatus(false, 0)
					}
				case types.ParamTypeDevice:
					// Pour les paramètres de device, on ne simule pas directement la réponse
					// mais on envoie un message OSC à Live qui va ensuite renvoyer les informations complètes
					if learnData.DeviceIndex != nil && learnData.ParamIndex != nil {
						// Forcer le slotIndex à être celui sur lequel l'utilisateur a cliqué
						m.state.Mutex.Lock()
						oldActiveSlot := m.state.ActiveSlot
						m.state.ActiveSlot = &slotIndex
						m.state.IsLearning = true
						m.state.Mutex.Unlock()

						// Mettre à jour l'affichage du statut d'apprentissage
						m.displayManager.UpdateLearningStatus(true, slotIndex)

						// Envoyer un message OSC à Live pour demander les informations du paramètre
						deviceIndex := -3 // Valeur par défaut pour le device sélectionné
						if learnData.DeviceIndex != nil {
							deviceIndex = *learnData.DeviceIndex
						}
						paramIndex := 0
						if learnData.ParamIndex != nil {
							paramIndex = *learnData.ParamIndex
						}

						log.Printf("Envoi du message /live/device/learning/parameter/value pour le device %d, paramètre %d",
							deviceIndex, paramIndex)

						// Envoyer le message OSC comme dans la version JS
						// Le premier argument doit être -3 (valeur spéciale pour indiquer un device normal)
						m.BaseMode.Send("/live/device/learning/parameter/value",
							[]interface{}{-3, deviceIndex, paramIndex})

						// Note: Live va répondre avec un message qui sera traité par handleDeviceLearningParamValue
						// Pas besoin d'appeler directement le handler ici

						// Restaurer l'ancien slot actif si nécessaire
						if oldActiveSlot != nil && *oldActiveSlot != slotIndex {
							m.state.Mutex.Lock()
							m.state.ActiveSlot = oldActiveSlot
							m.state.Mutex.Unlock()
						}
					}
				}

				// Pour les paramètres de device (type 4) et les paramètres de chaîne (types 5, 9, 10),
				// gérer différemment le nettoyage des données d'apprentissage
				if modeManager != nil {
					switch learnData.ParamType {
					case types.ParamTypeDevice:
						// Pour les paramètres de device, ne pas vider les données d'apprentissage
						// car nous attendons encore la réponse de Live
						log.Println("Conservation des données d'apprentissage pour le paramètre de device en attente de réponse")
					case types.ParamTypeChainVolume, types.ParamTypeChainPan, types.ParamTypeChainMute, types.ParamTypeChainSolo:
						// Pour les paramètres de chaîne, vider les données d'apprentissage après simulation
						modeManager.SetLearnData(nil)
						log.Println("Données d'apprentissage vidées après simulation de paramètre de chaîne")
					default:
						// Pour les autres types de paramètres, vider les données d'apprentissage
						modeManager.SetLearnData(nil)
						log.Println("Données d'apprentissage vidées après simulation")
					}
				}
			} else {
				// SEULEMENT si learnData est vide, gérer le learning en fonction de l'état précédent
				if m.state.IsLearning && m.state.ActiveSlot != nil && *m.state.ActiveSlot == slotIndex {
					// Si on touche le même slot qui est déjà en apprentissage, arrêter l'apprentissage
					log.Println("Arrêt de l'apprentissage car même slot touché")
					m.StopLearning()
				} else {
					// Sinon, démarrer l'apprentissage pour ce slot
					log.Println("Démarrage de l'apprentissage pour un nouveau slot")
					m.StartLearning(slotIndex)
				}
			}
		} else {
			// Si le slot contient déjà un paramètre, le sélectionner
			log.Printf("Sélection du slot %d qui contient déjà un paramètre", slotIndex)

			// Mettre à jour l'état interne pour indiquer que ce slot est sélectionné
			m.state.Mutex.Lock()
			m.state.ActiveSlot = &slotIndex
			m.state.Mutex.Unlock()

			// Envoyer un message OSC pour indiquer que le slot est sélectionné
			// Cela déclenchera les handlers appropriés pour mettre à jour l'interface
			m.BaseMode.Send(OscAddressLearnSlot, []interface{}{slotIndex})

			// Mettre à jour l'affichage directement
			// Calculer l'index relatif du slot sur la page courante
			startSlot := (m.state.CurrentPage - 1) * SlotsPerPage
			relativeIndex := slotIndex - startSlot

			// Vérifier si le slot est visible sur la page courante
			if relativeIndex >= 0 && relativeIndex < SlotsPerPage {
				// Envoyer le message de sélection du slot
				m.commManager.SendMessage(fmt.Sprintf("sl,%d", relativeIndex), m.isActive)
			}
		}

	case "lp":
		// Toucher long sur un slot (pour le supprimer)
		slotIndex := index + SlotsPerPage*(m.state.CurrentPage-1)
		m.ClearSlot(slotIndex)

	case "ly":
		// Toucher long sur un slot pour définir une valeur spécifique
		// Calculer l'index absolu du slot basé sur la page courante
		absoluteSlotIndex := index + SlotsPerPage*(m.state.CurrentPage-1)

		// Récupérer les informations du slot
		m.state.Mutex.RLock()
		slot, exists := m.state.Slots[absoluteSlotIndex]
		hasType := exists && slot.Type != nil
		m.state.Mutex.RUnlock()

		if !exists || !hasType {
			log.Printf("Slot %d non trouvé ou sans type défini", absoluteSlotIndex)
			return
		}

		// Traiter différemment selon le type de paramètre
		switch *slot.Type {
		case types.ParamTypeVolume: // 1
			// Pour le volume, définir une valeur de 0.85 (environ 0dB)
			m.BaseMode.Send("/live/learn/set", []interface{}{absoluteSlotIndex, 0.85})

		case types.ParamTypePan: // 2
			// Pour le panoramique, définir une valeur de 0 (centre)
			m.BaseMode.Send("/live/learn/set", []interface{}{absoluteSlotIndex, 0.0})

		case types.ParamTypeSend: // 3
			// Pour les sends, utiliser l'index de send mais envoyer seulement l'index et la valeur
			m.BaseMode.Send("/live/learn/set", []interface{}{absoluteSlotIndex, 0.0})

		case types.ParamTypeDevice: // 4
			// Pour les paramètres de device, traitement spécial pour les paramètres quantifiés
			if slot.IsQuantized != nil && *slot.IsQuantized {
				// Si la valeur actuelle est égale à la valeur maximale, revenir à la valeur minimale
				var nextValue float64
				if slot.Value != nil && slot.Max != nil && slot.Min != nil {
					if *slot.Value >= *slot.Max {
						nextValue = *slot.Min
					} else {
						nextValue = *slot.Value + 1
					}
				} else {
					// Valeurs par défaut si les informations sont manquantes
					nextValue = 0.0
				}

				m.BaseMode.Send("/live/learn/set", []interface{}{absoluteSlotIndex, nextValue})
			} else {
				log.Printf("Le paramètre du slot %d n'est pas quantifié, aucune action nécessaire", absoluteSlotIndex)
			}

		case types.ParamTypeChainVolume: // 5
			// Pour le volume de chaîne, définir une valeur de 0.85 (environ 0dB)
			m.BaseMode.Send("/live/learn/set", []interface{}{absoluteSlotIndex, 0.85})

		case types.ParamTypeChainPan: // 6
			// Pour le panoramique de chaîne, définir une valeur de 0 (centre)
			m.BaseMode.Send("/live/learn/set", []interface{}{absoluteSlotIndex, 0.0})

		case types.ParamTypeMute: // 7
			// Pour mute, définir une valeur de 1 (activé)
			m.BaseMode.Send("/live/learn/set", []interface{}{absoluteSlotIndex, 1.0})

		case types.ParamTypeSolo: // 8
			// Pour solo, définir une valeur de 1 (activé)
			m.BaseMode.Send("/live/learn/set", []interface{}{absoluteSlotIndex, 1.0})

		case types.ParamTypeChainMute: // 9
			// Pour mute de chaîne, définir une valeur de 1 (activé)
			m.BaseMode.Send("/live/learn/set", []interface{}{absoluteSlotIndex, 1.0})

		case types.ParamTypeChainSolo: // 10
			// Pour solo de chaîne, définir une valeur de 1 (activé)
			m.BaseMode.Send("/live/learn/set", []interface{}{absoluteSlotIndex, 1.0})
		}


	case "lc":
		// Toucher long sur le bouton "clear" (pour nettoyer tous les slots)
		m.ClearAllSlots()
		// Mettre à jour l'affichage de la page courante
		m.displayManager.UpdatePage(m.state.CurrentPage)
	}
}