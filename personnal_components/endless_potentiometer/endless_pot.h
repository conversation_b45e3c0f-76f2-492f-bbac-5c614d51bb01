#pragma once

#include <cstdint>
#include <cmath>

namespace endless_pot {

// Function to check if potentiometer logs are enabled
// This is implemented in the component to avoid dependency issues
bool potentiometer_logs_are_enabled();

// Constantes pour la direction
enum PotDirection {
    COUNTER_CLOCKWISE = -1,
    NOT_MOVING = 0,
    CLOCKWISE = 1
};

// Constantes pour l'état des balais
enum WiperState {
    DOWN = -1,
    STATIONARY = 0,
    UP = 1
};

class EndlessPot {
public:
    // Configuration
    uint16_t threshold = 1;         // Seuil de détection de mouvement (à ajuster)
    uint16_t sensitivity = 2;       // Sensibilité pour le calcul du delta (1 = max sensible)
    uint16_t adcMaxValue = 4095;    // Valeur max de l'ADC (12 bits)
    uint16_t adcHalfValue = 2048;   // Moitié de la plage ADC
    uint16_t safetyNet = 50;        // Marge pour la "safety net" (à ajuster)
    uint16_t adc02Value = 819;      // ~ 0.2 * 4095
    uint16_t adc08Value = 3276;     // ~ 0.8 * 4095

    // État interne
    uint16_t valueA = 0;
    uint16_t valueB = 0;
    uint16_t previousValueA = 0;
    uint16_t previousValueB = 0;
    WiperState dirA = STATIONARY;
    WiperState dirB = STATIONARY;
    PotDirection direction = NOT_MOVING;
    PotDirection previousDirection = NOT_MOVING;
    bool isMoving = false;
    int valueChanged = 0; // Peut être négatif si on veut un delta signé directement
    /**
     * @brief Accumule les variations inférieures à la sensibilité afin qu'elles
     *        ne soient pas perdues et soient restituées dès qu'elles atteignent
     *        le seuil. Permet de gagner en précision sur les micro-mouvements.
     */
    int pendingChange = 0;

    /**
     * Gestion du flush automatique : si l'utilisateur bouge très lentement,
     * les micro-incréments accumulés doivent quand même sortir après un
     * certain temps pour que l'interface reste réactive.
     */
    static const int32_t DEFAULT_FLUSH_TIMEOUT_US = 150000; // 150 ms
    int32_t flushTimeoutUs = DEFAULT_FLUSH_TIMEOUT_US;
    int64_t lastFlushMicros = 0;

    // Permet d'ajuster dynamiquement le délai de flush
    void setFlushTimeoutUs(int32_t us) {
        flushTimeoutUs = (us <= 0) ? DEFAULT_FLUSH_TIMEOUT_US : us;
        lastFlushMicros = 0; // redémarre le compteur
    }

    EndlessPot(); // Constructeur par défaut

    // Constructeur avec paramètres de configuration
    EndlessPot(uint16_t threshold, uint16_t sensitivity, uint16_t adcMaxValue);

    // Mise à jour des valeurs et calcul de la direction et du delta
    void updateValues(uint16_t newA, uint16_t newB);

    // Setter permettant d'ajuster dynamiquement la sensibilité depuis le code appelant
    void setSensitivity(uint16_t newSensitivity) { sensitivity = (newSensitivity == 0) ? 1 : newSensitivity; }
};

} // namespace endless_pot
