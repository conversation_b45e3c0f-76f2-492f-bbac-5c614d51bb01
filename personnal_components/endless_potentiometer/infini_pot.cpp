#include "infini_pot.h"
#include "esp_timer.h"
#include "esp_log.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include <algorithm>
#include <cmath>

namespace endless_pot {

InfiniPot::InfiniPot() {
    // Constructeur par défaut
}

InfiniPot::InfiniPot(int dzThreshold, int dzDecayDivisor, int dzMaxValue)
    : dzThreshold(dzThreshold), dzDecayDivisor(dzDecayDivisor), dzMaxValue(dzMaxValue) {
}

void InfiniPot::init() {
    // Initialiser le temps précédent
    previousMicros = esp_timer_get_time();
    pot_value = 0;
    dzValue = 0;
    lastDelta = 0;
}

void InfiniPot::update(uint16_t rawA, uint16_t rawB) {
    int64_t currentMicros = esp_timer_get_time();

    // Mettre à jour les valeurs brutes pour le debug
    lastRawA = rawA;
    lastRawB = rawB;

    // Mettre à jour l'état du potentiomètre sans fin
    endlessPot.updateValues(rawA, rawB);

    // Logique de la zone morte (Dead Zone) et mise à jour de la valeur absolue
    if (endlessPot.isMoving) {
        int delta = 0;
        int64_t deltaMicros = currentMicros - previousMicros;

        // Le delta est la magnitude (endlessPot.valueChanged) avec le signe de la direction
        delta = endlessPot.valueChanged * endlessPot.direction;
        lastDelta = delta;

        // Mise à jour de la valeur de la zone morte
        dzValue = std::max(dzValue, 0); // S'assurer qu'elle ne devienne pas négative par la décroissance seule
        dzValue += std::abs(delta);    // Augmenter avec le mouvement détecté
        dzValue = std::min(dzValue, dzMaxValue); // Plafonner

        // Gestion de la dead-zone : on accumule le delta si la DZ n'est pas franchie
        if (dzValue > dzThreshold) {
            stepAccumulator += delta;  // auteurise l'avancée
        } else {
            // Dead-zone active : on empile le delta mais il ne sortira que lorsque dzValue retombera
            stepAccumulator += delta;
        }

        // Sortie progressive : on délivre AU MAXIMUM un pas par update, pour éviter les bonds
        if (stepAccumulator > 0) {
            pot_value += 1;
            stepAccumulator -= 1;
        } else if (stepAccumulator < 0) {
            pot_value -= 1;
            stepAccumulator += 1;
        }

        // Faire décroître la valeur de la zone morte avec le temps écoulé
        if (deltaMicros > 0 && dzDecayDivisor > 0) {
            dzValue -= deltaMicros / dzDecayDivisor;
        }
        previousMicros = currentMicros; // Mémoriser le temps de ce cycle
    } else {
        // Si pas de mouvement détecté par EndlessPot, faire quand même décroître dzValue
        int64_t deltaMicros = currentMicros - previousMicros;
        if (deltaMicros > 0 && dzDecayDivisor > 0) {
            dzValue -= deltaMicros / dzDecayDivisor;
            dzValue = std::max(dzValue, 0); // Empêcher d'être négatif
        }
        // Ne pas mettre à jour previousMicros ici pour que la décroissance continue
        // basée sur le temps depuis le dernier mouvement significatif
    }
}

void InfiniPot::updateWithMedian(const std::vector<uint16_t>& readingsA, const std::vector<uint16_t>& readingsB) {
    if (readingsA.size() < READS || readingsB.size() < READS) {
        // Pas assez de lectures
        return;
    }

    // Copier les vecteurs pour ne pas modifier les originaux
    std::vector<uint16_t> sortedA = readingsA;
    std::vector<uint16_t> sortedB = readingsB;

    // Trier pour trouver la médiane
    std::sort(sortedA.begin(), sortedA.end());
    std::sort(sortedB.begin(), sortedB.end());

    // Utiliser la médiane
    uint16_t medianA = sortedA[MEDIAN_IDX];
    uint16_t medianB = sortedB[MEDIAN_IDX];

    // Mettre à jour avec les médianes
    update(medianA, medianB);
}

void InfiniPot::runReadLoop(
    std::function<uint16_t(uint8_t)> readADCFunc,
    uint8_t channelA,
    uint8_t channelB,
    uint32_t delayMs,
    std::function<void(InfiniPot&)> onUpdateCallback
) {
    static const char *TAG = "InfiniPot";

    // Vérifier si les logs sont activés avant d'afficher le message
    if (potentiometer_logs_are_enabled()) {
        ESP_LOGI(TAG, "Démarrage de la boucle de lecture du potentiomètre sans fin...");
    }

    // Initialiser le potentiomètre sans fin si ce n'est pas déjà fait
    init();

    // Tampons pour les lectures brutes (pour la médiane)
    std::vector<uint16_t> readingsA(READS);
    std::vector<uint16_t> readingsB(READS);

    while (true) {
        // 1. Lire les valeurs brutes avec filtrage médian
        for (int i = 0; i < READS; ++i) {
            readingsA[i] = readADCFunc(channelA);
            readingsB[i] = readADCFunc(channelB);
        }

        // Permettre au scheduler de s'exécuter après les lectures
        vTaskDelay(pdMS_TO_TICKS(2));

        // 2. Mettre à jour l'état du potentiomètre sans fin avec les lectures médianes
        updateWithMedian(readingsA, readingsB);

        // 3. Appeler la fonction de rappel si elle est définie
        if (onUpdateCallback) {
            onUpdateCallback(*this);
        }

        // Attente avant la prochaine lecture
        vTaskDelay(pdMS_TO_TICKS(delayMs));
    }
}

} // namespace endless_pot
