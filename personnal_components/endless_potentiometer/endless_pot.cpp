#include "endless_pot.h"
#include <cmath>
#include "esp_timer.h"

namespace endless_pot {

EndlessPot::EndlessPot() {
    // Constructeur par défaut avec les valeurs par défaut déjà initialisées
}

EndlessPot::EndlessPot(uint16_t threshold, uint16_t sensitivity, uint16_t adcMaxValue)
    : threshold(threshold), sensitivity(sensitivity), adcMaxValue(adcMaxValue) {
    // Recalculer les valeurs dérivées
    adcHalfValue = adcMaxValue / 2;
    adc02Value = static_cast<uint16_t>(adcMaxValue * 0.2);
    adc08Value = static_cast<uint16_t>(adcMaxValue * 0.8);
}

void EndlessPot::updateValues(uint16_t newA, uint16_t newB) {
    previousValueA = valueA;
    previousValueB = valueB;
    valueA = newA;
    valueB = newB;

    // 1. Déterminer la direction de chaque balai (Wiper)
    if (valueA >= (previousValueA + threshold)) dirA = UP;
    else if (valueA <= (previousValueA - threshold)) dirA = DOWN;
    else dirA = STATIONARY;

    if (valueB >= (previousValueB + threshold)) dirB = UP;
    else if (valueB <= (previousValueB - threshold)) dirB = DOWN;
    else dirB = STATIONARY;

    // 2. Évaluer la direction globale du potentiomètre
    PotDirection currentDirection = NOT_MOVING; // Direction pour cette mise à jour

    if (dirA == DOWN && dirB == DOWN) {
        currentDirection = (valueA > valueB) ? CLOCKWISE : COUNTER_CLOCKWISE;
    } else if (dirA == UP && dirB == UP) {
        currentDirection = (valueA < valueB) ? CLOCKWISE : COUNTER_CLOCKWISE;
    } else if (dirA == UP && dirB == DOWN) {
        // Logique "safety net"
        if ((valueA > adcHalfValue) || (valueB > adcHalfValue)) {
            if (((previousValueA < adcHalfValue) && (previousValueB < safetyNet)) ||
                ((previousValueA < adcHalfValue) && (previousValueB > (adcMaxValue - safetyNet))))
            {
                currentDirection = previousDirection; // Utiliser la direction précédente dans ce cas ambigu
            } else {
                currentDirection = CLOCKWISE;
            }
        } else {
            currentDirection = COUNTER_CLOCKWISE;
        }
    } else if (dirA == DOWN && dirB == UP) {
        // Logique "safety net"
        if ((valueA < adcHalfValue) || (valueB < adcHalfValue)) {
            if (((previousValueA > adcHalfValue) && (previousValueB < safetyNet)) ||
                ((previousValueA > adcHalfValue) && (previousValueB > (adcMaxValue - safetyNet))))
            {
                currentDirection = previousDirection; // Utiliser la direction précédente dans ce cas ambigu
            } else {
                currentDirection = CLOCKWISE;
            }
        } else {
            currentDirection = COUNTER_CLOCKWISE;
        }
    } else {
        currentDirection = NOT_MOVING;
    }

    direction = currentDirection;
    isMoving = (direction != NOT_MOVING);

    if (isMoving) {
        previousDirection = direction; // Mémoriser la dernière direction connue
    }

    // 3. Calculer la magnitude du changement (valueChanged) avec accumulation des micro-mouvements
    valueChanged = 0;

    if (dirA != STATIONARY && dirB != STATIONARY) {
        // Mesurer les deux écarts pour garder la plus petite variation fiable
        int deltaA = std::abs((int)valueA - (int)previousValueA);
        int deltaB = std::abs((int)valueB - (int)previousValueB);

        bool aInLinear = (valueA < adc08Value) && (valueA > adc02Value);
        bool bInLinear = (valueB < adc08Value) && (valueB > adc02Value);

        int deltaCandidate = 0;
        if (aInLinear && bInLinear) {
            deltaCandidate = std::min(deltaA, deltaB); // privilégier la plus petite variation
        } else if (aInLinear) {
            deltaCandidate = deltaA;
        } else if (bInLinear) {
            deltaCandidate = deltaB;
        } else {
            deltaCandidate = std::min(deltaA, deltaB); // zone non linéaire : on prend la plus petite pour éviter le saut
        }

        // 3.2 Accumulation dans l'attente d'atteindre le seuil de sensibilité
        pendingChange += deltaCandidate;

        if (sensitivity > 0) {
            valueChanged = pendingChange / sensitivity;    // Partie entière : variation utilisable
            pendingChange = pendingChange % sensitivity;    // Reste conservé pour la prochaine fois
        } else {
            valueChanged = pendingChange; // Sensibilité = 1 forcée au setter
            pendingChange = 0;
        }
    }
    // valueChanged est positif. Le signe global sera appliqué par 'direction'.

    /**
     * 4. Flush automatique des micro-variations : si `pendingChange` reste
     *    inférieur au seuil pendant trop longtemps, on force la sortie d'un
     *    pas pour éviter la sensation de « latence ».
     */
    int64_t now = esp_timer_get_time();

    // Initialisation du timer de flush au premier passage
    if (lastFlushMicros == 0) {
        lastFlushMicros = now;
    }

    if (valueChanged == 0 && pendingChange > 0 && direction != NOT_MOVING) {
        if ((now - lastFlushMicros) > flushTimeoutUs) {
            valueChanged = 1;                 // Injecte un pas minimal
            if (pendingChange > 0) {
                int dec = (sensitivity > 0) ? sensitivity : 1;
                pendingChange = (pendingChange > dec) ? (pendingChange - dec) : 0;
            }
            lastFlushMicros = now;
        }
    } else if (valueChanged > 0) {
        // Réinitialiser le timer dès qu'un pas "normal" est sorti
        lastFlushMicros = now;
    }
}

} // namespace endless_pot
