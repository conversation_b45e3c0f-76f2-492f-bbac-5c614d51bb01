#pragma once

#include <cstdint>
#include <vector>
#include <algorithm>
#include <functional>
#include "endless_pot.h"

namespace endless_pot {

class InfiniPot {
public:
    // Configuration
    static const int READS = 5;              // Nombre de lectures pour la médiane
    static const int MEDIAN_IDX = READS / 2; // Index de la médiane (pour READS=5, MEDIAN_IDX=2)
    static const int MAX_POT_VALUE = 4095;   // Valeur absolue maximale (0-4095)
    static const int DZ_THRESHOLD = 8;      // Seuil de la zone morte pour déclencher une mise à jour
    static const int DZ_DECAY_DIVISOR = 5000; // Diviseur pour la décroissance de dzValue (micros)
    static const int DZ_MAX_VALUE = 80;      // Valeur maximale pour dzValue

    // Constructeur
    InfiniPot();

    // Constructeur avec paramètres
    InfiniPot(int dzThreshold, int dzDecayDivisor, int dzMaxValue);

    // Initialisation
    void init();

    // Mise à jour avec les nouvelles valeurs ADC
    void update(uint16_t rawA, uint16_t rawB);

    // Mise à jour avec des vecteurs de lectures (pour filtrage médian)
    void updateWithMedian(const std::vector<uint16_t>& readingsA, const std::vector<uint16_t>& readingsB);

    /**
     * @brief Boucle de lecture continue du potentiomètre avec une fonction de lecture ADC
     *
     * Cette méthode prend en charge la lecture des canaux ADC et la mise à jour de l'état du potentiomètre.
     * Elle utilise une fonction de rappel pour lire les valeurs ADC, ce qui permet de l'utiliser avec
     * différents types d'ADC.
     *
     * @param readADCFunc Fonction qui prend un numéro de canal et retourne la valeur ADC
     * @param channelA Numéro du canal pour la première sortie du potentiomètre
     * @param channelB Numéro du canal pour la deuxième sortie du potentiomètre
     * @param delayMs Délai en millisecondes entre les lectures (pour le scheduler)
     * @param onUpdateCallback Fonction appelée après chaque mise à jour (optionnelle)
     */
    void runReadLoop(
        std::function<uint16_t(uint8_t)> readADCFunc,
        uint8_t channelA,
        uint8_t channelB,
        uint32_t delayMs,
        std::function<void(InfiniPot&)> onUpdateCallback = nullptr
    );

    // Accesseurs
    int getPotValue() const { return pot_value; }
    int getDzValue() const { return dzValue; }
    int getLastDelta() const { return lastDelta; }
    uint16_t getLastRawA() const { return lastRawA; }
    uint16_t getLastRawB() const { return lastRawB; }
    bool isMoving() const { return endlessPot.isMoving; }
    PotDirection getDirection() const { return endlessPot.direction; }

private:
    EndlessPot endlessPot;
    int pot_value = 0;           // Valeur absolue courante du potentiomètre (non bornée)
    int dzValue = 0;             // Valeur de la zone morte dynamique
    int64_t previousMicros = 0;  // Temps de la dernière mise à jour significative
    int lastDelta = 0;           // Dernier delta appliqué
    uint16_t lastRawA = 0;       // Dernière valeur brute A (pour debug)
    uint16_t lastRawB = 0;       // Dernière valeur brute B (pour debug)

    // Accumulation globale, délivrée pas à pas pour éviter les bonds
    int stepAccumulator = 0;   // somme des deltas à appliquer progressivement

    // Paramètres configurables
    int dzThreshold = DZ_THRESHOLD;
    int dzDecayDivisor = DZ_DECAY_DIVISOR;
    int dzMaxValue = DZ_MAX_VALUE;

    // Fonction helper pour le tri de la médiane
    static bool sort_desc(int a, int b) {
        return a > b;
    }
};

} // namespace endless_pot
