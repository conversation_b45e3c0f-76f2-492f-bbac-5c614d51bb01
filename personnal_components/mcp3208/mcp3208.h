#pragma once

#include <cstdint>
#include "driver/spi_master.h"
#include "driver/gpio.h"
#include "esp_err.h"
#include "esp_log.h"

// Définitions pour le MCP3208
#define MCP3208_START_BIT   0x04    // Bit de démarrage
#define MCP3208_SINGLE_ENDED 0x02   // Mode single-ended (vs différentiel)
#define MCP3208_CHANNEL_0   0x00    // Canal 0
#define MCP3208_CHANNEL_1   0x01    // Canal 1

// Configuration SPI pour le MCP3208
#define SPI_HOST        SPI2_HOST
#define PIN_NUM_MISO    GPIO_NUM_31
#define PIN_NUM_MOSI    GPIO_NUM_29
#define PIN_NUM_CLK     GPIO_NUM_30
#define PIN_NUM_CS1     GPIO_NUM_28  // CS pour le premier MCP3208
#define PIN_NUM_CS2     GPIO_NUM_34  // CS pour le deuxième MCP3208

/**
 * @brief Classe pour interfacer avec le convertisseur analogique-numérique MCP3208
 *
 * Cette classe gère la communication SPI avec le MCP3208 et permet de lire
 * les valeurs analogiques des différents canaux.
 */
class MCP3208
{
private:
    spi_device_handle_t spi;
    static bool spi_initialized;

    /**
     * @brief Initialise le bus SPI pour la communication avec le MCP3208
     *
     * @param cs_pin Numéro de GPIO pour le Chip Select
     * @return esp_err_t ESP_OK en cas de succès, code d'erreur sinon
     */
    esp_err_t initSPI(gpio_num_t cs_pin);

public:
    /**
     * @brief Constructeur qui initialise la communication SPI avec un CS spécifique
     *
     * @param cs_pin Numéro de GPIO pour le Chip Select
     */
    MCP3208(gpio_num_t cs_pin = PIN_NUM_CS1);

    /**
     * @brief Destructeur qui libère les ressources SPI
     */
    ~MCP3208();

    /**
     * @brief Lit la valeur analogique d'un canal spécifique
     *
     * @param channel Numéro du canal à lire (0-7)
     * @return uint16_t Valeur ADC lue (0-4095)
     */
    uint16_t readChannel(uint8_t channel);

    /**
     * @brief Convertit une valeur ADC en tension (mV)
     *
     * @param adcValue Valeur ADC à convertir
     * @param vRef Tension de référence en mV (par défaut 5000mV)
     * @return uint16_t Tension en mV
     */
    uint16_t convertToVoltage(uint16_t adcValue, uint16_t vRef = 5000);
};
