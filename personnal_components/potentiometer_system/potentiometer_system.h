#pragma once

#include "dual_mcp3208.h"
#include "infini_pot.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "esp_log.h"
#include "esp_system.h"
#include <functional>
#include <vector>
#include <iostream>

// Déclaration de la fonction externe pour envoyer des messages via le communication manager
#ifdef __cplusplus
extern "C" {
#endif
    extern void sendMessage(const char *message);
#ifdef __cplusplus
}
#endif

/**
 * @brief Initialise le système de potentiomètres avec une configuration par défaut
 *
 * Cette fonction initialise le système de potentiomètres avec une configuration par défaut
 * qui comprend trois potentiomètres:
 * - Pot1: ADC1, canaux 0 et 1
 * - Pot2: ADC2, canaux 0 et 1
 * - Pot3: ADC1, canaux 2 et 3
 *
 * @return true si l'initialisation a réussi, false sinon
 */
bool potentiometer_system_init();

/**
 * @brief Active ou désactive les logs des potentiomètres
 *
 * @param enable true pour activer les logs, false pour les désactiver
 */
void potentiometer_logs_enable(bool enable);

/**
 * @brief Vérifie si les logs des potentiomètres sont activés
 *
 * @return true si les logs sont activés, false sinon
 */
bool potentiometer_logs_are_enabled();

/**
 * @brief Obtient la valeur actuelle d'un potentiomètre par son index
 *
 * @param index Index du potentiomètre (0 pour Pot1, 1 pour Pot2, 2 pour Pot3)
 * @return int Valeur du potentiomètre
 */
int potentiometer_get_value(size_t index);

/**
 * @brief Vérifie si un potentiomètre est en mouvement
 *
 * @param index Index du potentiomètre (0 pour Pot1, 1 pour Pot2, 2 pour Pot3)
 * @return true si le potentiomètre est en mouvement, false sinon
 */
bool potentiometer_is_moving(size_t index);

/**
 * @brief Obtient la direction de rotation d'un potentiomètre
 *
 * @param index Index du potentiomètre (0 pour Pot1, 1 pour Pot2, 2 pour Pot3)
 * @return endless_pot::PotDirection Direction de rotation
 */
endless_pot::PotDirection potentiometer_get_direction(size_t index);

/**
 * @brief Classe qui encapsule le système de potentiomètres sans fin avec double MCP3208
 *
 * Cette classe gère l'initialisation et la lecture de plusieurs potentiomètres sans fin
 * connectés à deux MCP3208. Elle crée et gère les tâches FreeRTOS nécessaires et fournit
 * une interface simple pour accéder aux valeurs des potentiomètres.
 */
class PotentiometerSystem {
public:
    /**
     * @brief Fréquence de polling globale en millisecondes pour tous les potentiomètres
     */
    static int pollingFreq;

    /**
     * @brief Structure pour configurer un potentiomètre
     */
    struct PotConfig {
        uint8_t adcIndex;    // Index de l'ADC (0 pour ADC1, 1 pour ADC2)
        uint8_t channelA;    // Canal A du potentiomètre
        uint8_t channelB;    // Canal B du potentiomètre
        const char* name;    // Nom du potentiomètre (pour le log)
    };

    // Variable statique pour contrôler l'activation des logs
    static bool logsEnabled;

    /**
     * @brief Constructeur
     *
     * @param cs_pin1 Pin CS pour le premier MCP3208
     * @param cs_pin2 Pin CS pour le deuxième MCP3208
     */
    PotentiometerSystem(gpio_num_t cs_pin1 = PIN_NUM_CS1, gpio_num_t cs_pin2 = PIN_NUM_CS2);

    /**
     * @brief Destructeur
     */
    ~PotentiometerSystem();

    /**
     * @brief Initialise le DualMCP3208 avec un mutex SPI
     *
     * @param spi_mutex Mutex pour protéger l'accès au bus SPI
     * @return true si l'initialisation a réussi, false sinon
     */
    bool initDualADC(SemaphoreHandle_t spi_mutex);

    /**
     * @brief Initialise le système de potentiomètres
     *
     * @param potConfigs Vecteur de configurations de potentiomètres
     * @return true si l'initialisation a réussi, false sinon
     */
    bool begin(const std::vector<PotConfig>& potConfigs);

    /**
     * @brief Obtient la valeur actuelle d'un potentiomètre
     *
     * @param index Index du potentiomètre
     * @return int Valeur du potentiomètre
     */
    int getPotValue(size_t index) const;

    /**
     * @brief Vérifie si un potentiomètre est en mouvement
     *
     * @param index Index du potentiomètre
     * @return true si le potentiomètre est en mouvement, false sinon
     */
    bool isPotMoving(size_t index) const;

    /**
     * @brief Obtient la valeur de la zone morte d'un potentiomètre
     *
     * @param index Index du potentiomètre
     * @return int Valeur de la zone morte
     */
    int getPotDzValue(size_t index) const;

    /**
     * @brief Obtient le dernier delta appliqué à un potentiomètre
     *
     * @param index Index du potentiomètre
     * @return int Dernier delta
     */
    int getPotLastDelta(size_t index) const;

    /**
     * @brief Obtient la direction de rotation d'un potentiomètre
     *
     * @param index Index du potentiomètre
     * @return endless_pot::PotDirection Direction de rotation
     */
    endless_pot::PotDirection getPotDirection(size_t index) const;

    /**
     * @brief Définit une fonction de rappel pour un potentiomètre
     *
     * @param index Index du potentiomètre
     * @param callback Fonction de rappel
     */
    void setPotCallback(size_t index, std::function<void(endless_pot::InfiniPot&)> callback);

private:
    DualMCP3208 dualADC;  // Gestion de deux MCP3208 avec CS différents
    std::vector<endless_pot::InfiniPot> infiniPots;  // Potentiomètres sans fin
    std::vector<PotConfig> potConfigs;  // Configurations des potentiomètres
    std::vector<TaskHandle_t> taskHandles;  // Handles des tâches FreeRTOS
    std::vector<std::function<void(endless_pot::InfiniPot&)>> callbacks;  // Fonctions de rappel

    // Tâche FreeRTOS pour lire un potentiomètre
    static void potentiometerTask(void* pvParameters);

    // Structure pour passer les paramètres à la tâche
    struct TaskParams {
        PotentiometerSystem* system;
        size_t potIndex;
    };
    std::vector<TaskParams*> taskParams;  // Paramètres des tâches
};
