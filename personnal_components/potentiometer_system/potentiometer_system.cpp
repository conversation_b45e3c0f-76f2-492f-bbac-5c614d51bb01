#include "potentiometer_system.h"

static const char *TAG = "PotSystem";

// Instance globale du système de potentiomètres
static PotentiometerSystem* g_potSystem = nullptr;

// Initialisation de la variable statique (logs activés par défaut)
bool PotentiometerSystem::logsEnabled = true;

// Initialisation de la fréquence de polling globale (10ms par défaut)
int PotentiometerSystem::pollingFreq = 5;

// Fonction pour activer/désactiver les logs
void potentiometer_logs_enable(bool enable) {
    PotentiometerSystem::logsEnabled = enable;
    ESP_LOGI(TAG, "Logs des potentiomètres %s", enable ? "activés" : "désactivés");
}

// Fonction pour vérifier si les logs sont activés (utilisée par d'autres composants)
bool potentiometer_logs_are_enabled() {
    return PotentiometerSystem::logsEnabled;
}

// Override the function in the endless_pot namespace
namespace endless_pot {
    bool potentiometer_logs_are_enabled() {
        return ::potentiometer_logs_are_enabled();
    }
}

// Configuration par défaut des potentiomètres
static const std::vector<PotentiometerSystem::PotConfig> DEFAULT_POT_CONFIGS = {
    {0, 0, 1, "Pot1"}, // Premier potentiomètre sur ADC1, canaux 0 et 1
    {1, 0, 1, "Pot2"}, // Deuxième potentiomètre sur ADC2, canaux 0 et 1
    {0, 2, 3, "Pot3"}  // Troisième potentiomètre sur ADC1, canaux 2 et 3
};

// Initialisation du système de potentiomètres avec la configuration par défaut
bool potentiometer_system_init()
{
    std::cout << "Initialisation du système de potentiomètres..." << std::endl;

    // Créer l'instance si elle n'existe pas déjà
    if (g_potSystem == nullptr) {
        g_potSystem = new PotentiometerSystem(PIN_NUM_CS1, PIN_NUM_CS2);
    }

    // Créer un mutex SPI pour protéger l'accès au bus SPI
    SemaphoreHandle_t spi_mutex = xSemaphoreCreateMutex();
    if (spi_mutex == nullptr) {
        ESP_LOGE(TAG, "Impossible de créer le mutex SPI");
        return false;
    }
    ESP_LOGI(TAG, "Mutex SPI créé avec succès");

    // Initialiser le DualMCP3208 avec le mutex
    if (!g_potSystem->initDualADC(spi_mutex)) {
        ESP_LOGE(TAG, "Échec de l'initialisation du DualMCP3208 avec mutex");
        vSemaphoreDelete(spi_mutex);
        return false;
    }

    // Initialiser le système avec la configuration par défaut
    return g_potSystem->begin(DEFAULT_POT_CONFIGS);
}

// Obtient la valeur actuelle d'un potentiomètre par son index
int potentiometer_get_value(size_t index)
{
    if (g_potSystem == nullptr) {
        ESP_LOGE(TAG, "Le système de potentiomètres n'est pas initialisé");
        return 0;
    }

    return g_potSystem->getPotValue(index);
}

// Vérifie si un potentiomètre est en mouvement
bool potentiometer_is_moving(size_t index)
{
    if (g_potSystem == nullptr) {
        ESP_LOGE(TAG, "Le système de potentiomètres n'est pas initialisé");
        return false;
    }

    return g_potSystem->isPotMoving(index);
}

// Obtient la direction de rotation d'un potentiomètre
endless_pot::PotDirection potentiometer_get_direction(size_t index)
{
    if (g_potSystem == nullptr) {
        ESP_LOGE(TAG, "Le système de potentiomètres n'est pas initialisé");
        return endless_pot::NOT_MOVING;
    }

    return g_potSystem->getPotDirection(index);
}

// Constructeur
PotentiometerSystem::PotentiometerSystem(gpio_num_t cs_pin1, gpio_num_t cs_pin2)
    : dualADC(cs_pin1, cs_pin2)
{
    ESP_LOGI(TAG, "PotentiometerSystem initialisé avec CS1 sur GPIO %d et CS2 sur GPIO %d", cs_pin1, cs_pin2);
}

// Destructeur
PotentiometerSystem::~PotentiometerSystem()
{
    // Supprimer les tâches FreeRTOS
    for (auto handle : taskHandles) {
        if (handle != nullptr) {
            vTaskDelete(handle);
        }
    }

    // Libérer la mémoire des paramètres de tâche
    for (auto param : taskParams) {
        delete param;
    }
}

// Initialisation du DualMCP3208 avec un mutex SPI
bool PotentiometerSystem::initDualADC(SemaphoreHandle_t spi_mutex)
{
    return dualADC.begin(spi_mutex);
}

// Initialisation du système
bool PotentiometerSystem::begin(const std::vector<PotConfig>& configs)
{
    ESP_LOGI(TAG, "Initialisation du système de potentiomètres...");

    // Stocker les configurations
    potConfigs = configs;

    // Initialiser les potentiomètres
    infiniPots.resize(configs.size());
    callbacks.resize(configs.size(), nullptr);
    taskHandles.resize(configs.size(), nullptr);
    taskParams.resize(configs.size(), nullptr);

    // Créer les tâches pour chaque potentiomètre
    for (size_t i = 0; i < configs.size(); i++) {
        // Créer les paramètres de la tâche
        taskParams[i] = new TaskParams{this, i};

        // Créer la tâche
        char taskName[32];
        snprintf(taskName, sizeof(taskName), "Pot%zu_Task", i);

        // Utiliser une priorité plus élevée pour le potentiomètre 1
        int priority = 5;

        BaseType_t result = xTaskCreate(
            potentiometerTask,    // Fonction de la tâche
            taskName,             // Nom de la tâche
            4096,                 // Taille de la pile
            taskParams[i],        // Paramètre de la tâche
            priority,             // Priorité (plus élevée pour Pot1)
            &taskHandles[i]       // Handle de la tâche
        );

        if (result != pdPASS) {
            ESP_LOGE(TAG, "Échec de la création de la tâche pour le potentiomètre %zu", i);
            return false;
        }

        ESP_LOGI(TAG, "Tâche créée pour le potentiomètre %zu (%s)", i, configs[i].name);
    }

    return true;
}

// Tâche FreeRTOS pour lire un potentiomètre
void PotentiometerSystem::potentiometerTask(void* pvParameters)
{
    TaskParams* params = static_cast<TaskParams*>(pvParameters);
    PotentiometerSystem* system = params->system;
    size_t potIndex = params->potIndex;

    const PotConfig& config = system->potConfigs[potIndex];
    endless_pot::InfiniPot& pot = system->infiniPots[potIndex];

    ESP_LOGI(TAG, "Démarrage de la tâche pour le potentiomètre %zu (%s)", potIndex, config.name);

    // Obtenir la fonction de lecture pour l'ADC approprié
    auto readADCFunc = system->dualADC.getReadFunction(config.adcIndex);

    // Définir une fonction de rappel pour le potentiomètre
    auto displayCallback = [system, potIndex](endless_pot::InfiniPot& pot) {
        // Appeler la fonction de rappel personnalisée si elle existe
        if (system->callbacks[potIndex]) {
            system->callbacks[potIndex](pot);
        }

        // Vérifier si le potentiomètre a bougé et dépasse le seuil de zone morte
        if (pot.isMoving() && pot.getDzValue() > endless_pot::InfiniPot::DZ_THRESHOLD) {
            // Afficher la valeur si les logs sont activés
            if (PotentiometerSystem::logsEnabled) {
                ESP_LOGI(TAG, "%s Val: %d (Delta: %d, Raw A: %u, Raw B: %u, dz: %d)",
                         system->potConfigs[potIndex].name,
                         pot.getPotValue(), pot.getLastDelta(),
                         pot.getLastRawA(), pot.getLastRawB(),
                         pot.getDzValue());
            }

            // Envoyer un message au format "e,<indexdupot>,<delta>" via le communication manager
            char message[20];
            snprintf(message, sizeof(message), "e,%zu,%d", potIndex, pot.getLastDelta());
            sendMessage(message);
        }
    };

    // Lancer la boucle de lecture
    pot.runReadLoop(readADCFunc, config.channelA, config.channelB, PotentiometerSystem::pollingFreq, displayCallback);

    // Cette ligne ne devrait jamais être atteinte
    vTaskDelete(NULL);
}

// Obtient la valeur actuelle d'un potentiomètre
int PotentiometerSystem::getPotValue(size_t index) const
{
    if (index >= infiniPots.size()) {
        ESP_LOGE(TAG, "Index de potentiomètre invalide: %zu", index);
        return 0;
    }

    return infiniPots[index].getPotValue();
}

// Vérifie si un potentiomètre est en mouvement
bool PotentiometerSystem::isPotMoving(size_t index) const
{
    if (index >= infiniPots.size()) {
        ESP_LOGE(TAG, "Index de potentiomètre invalide: %zu", index);
        return false;
    }

    return infiniPots[index].isMoving();
}

// Obtient la valeur de la zone morte d'un potentiomètre
int PotentiometerSystem::getPotDzValue(size_t index) const
{
    if (index >= infiniPots.size()) {
        ESP_LOGE(TAG, "Index de potentiomètre invalide: %zu", index);
        return 0;
    }

    return infiniPots[index].getDzValue();
}

// Obtient le dernier delta appliqué à un potentiomètre
int PotentiometerSystem::getPotLastDelta(size_t index) const
{
    if (index >= infiniPots.size()) {
        ESP_LOGE(TAG, "Index de potentiomètre invalide: %zu", index);
        return 0;
    }

    return infiniPots[index].getLastDelta();
}

// Obtient la direction de rotation d'un potentiomètre
endless_pot::PotDirection PotentiometerSystem::getPotDirection(size_t index) const
{
    if (index >= infiniPots.size()) {
        ESP_LOGE(TAG, "Index de potentiomètre invalide: %zu", index);
        return endless_pot::NOT_MOVING;
    }

    return infiniPots[index].getDirection();
}

// Définit une fonction de rappel pour un potentiomètre
void PotentiometerSystem::setPotCallback(size_t index, std::function<void(endless_pot::InfiniPot&)> callback)
{
    if (index >= callbacks.size()) {
        ESP_LOGE(TAG, "Index de potentiomètre invalide: %zu", index);
        return;
    }

    callbacks[index] = callback;
}
