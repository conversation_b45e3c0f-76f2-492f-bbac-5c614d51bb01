#include "mcp23017_switches.h"

static const char *TAG = "MCP23017Switches";

// Instance globale du système de switchs
static MCP23017Switches* g_switchSystem = nullptr;

// Initialisation de la variable statique (logs activés par défaut)
bool MCP23017Switches::logsEnabled = true;

// Fonction pour activer/désactiver les logs
void mcp23017_switches_logs_enable(bool enable) {
    MCP23017Switches::logsEnabled = enable;
    ESP_LOGI(TAG, "Logs des switchs %s", enable ? "activés" : "désactivés");
}

// Fonction pour vérifier si les logs sont activés
bool mcp23017_switches_logs_are_enabled() {
    return MCP23017Switches::logsEnabled;
}

// Initialisation du système de switchs avec la configuration par défaut
bool mcp23017_switches_init()
{
    ESP_LOGI(TAG, "Initialisation du système de switchs MCP23017...");

    // Créer l'instance si elle n'existe pas déjà
    if (g_switchSystem == nullptr) {
        g_switchSystem = new MCP23017Switches();
    }

    // Initialiser le système
    return g_switchSystem->begin();
}

// Obtient l'état actuel d'un switch par son index
bool mcp23017_switch_get_state(size_t index)
{
    if (g_switchSystem == nullptr) {
        ESP_LOGE(TAG, "Le système de switchs n'est pas initialisé");
        return false;
    }

    return g_switchSystem->getSwitchState(index);
}

// Obtient l'état de tous les switchs
uint16_t mcp23017_switches_get_all_states()
{
    if (g_switchSystem == nullptr) {
        ESP_LOGE(TAG, "Le système de switchs n'est pas initialisé");
        return 0;
    }

    return g_switchSystem->getAllSwitchStates();
}

// Constructeur
MCP23017Switches::MCP23017Switches(i2c_port_t i2c_port, gpio_num_t sda_pin, 
                                   gpio_num_t scl_pin, uint8_t addr)
    : i2c_port(i2c_port), sda_pin(sda_pin), scl_pin(scl_pin), mcp_addr(addr),
      mcp_i2c_handle(nullptr), current_states(0), previous_states(0), read_task_handle(nullptr)
{
    ESP_LOGI(TAG, "MCP23017Switches initialisé avec SDA=%d, SCL=%d, Addr=0x%02X", 
             sda_pin, scl_pin, addr);
    
    // Initialiser le vecteur de callbacks pour 16 switchs
    callbacks.resize(16, nullptr);
    
    // Créer le mutex
    state_mutex = xSemaphoreCreateMutex();
}

// Destructeur
MCP23017Switches::~MCP23017Switches()
{
    // Supprimer la tâche FreeRTOS
    if (read_task_handle != nullptr) {
        vTaskDelete(read_task_handle);
    }
    
    // Supprimer le mutex
    if (state_mutex != nullptr) {
        vSemaphoreDelete(state_mutex);
    }
    
    // Supprimer le bus I2C du MCP23017
    if (mcp_i2c_handle != NULL) {
        i2c_del_master_bus(mcp_i2c_handle);
    }
}

// Initialisation du système
bool MCP23017Switches::begin()
{
    ESP_LOGI(TAG, "Initialisation du système de switchs MCP23017...");

    // Initialiser le bus I2C
    if (!initI2C()) {
        ESP_LOGE(TAG, "Échec de l'initialisation du bus I2C");
        return false;
    }

    // Initialiser le MCP23017
    if (!initMCP23017()) {
        ESP_LOGE(TAG, "Échec de l'initialisation du MCP23017");
        return false;
    }

    // Lire l'état initial des switchs
    current_states = readAllSwitches();
    previous_states = current_states;
    
    ESP_LOGI(TAG, "État initial des switchs: 0x%04X", current_states);

    // Créer la tâche de lecture périodique
    BaseType_t result = xTaskCreate(
        readTask,               // Fonction de la tâche
        "MCP23017_Read",        // Nom de la tâche
        4096,                   // Taille de la pile
        this,                   // Paramètre de la tâche
        5,                      // Priorité
        &read_task_handle       // Handle de la tâche
    );

    if (result != pdPASS) {
        ESP_LOGE(TAG, "Échec de la création de la tâche de lecture");
        return false;
    }

    ESP_LOGI(TAG, "Système de switchs MCP23017 initialisé avec succès");
    return true;
}

// Initialisation du bus I2C
bool MCP23017Switches::initI2C()
{
    // Créer un bus I2C séparé pour le MCP23017 sur GPIO 32/33
    i2c_master_bus_config_t i2c_bus_conf = {
        .i2c_port = i2c_port,
        .sda_io_num = sda_pin,
        .scl_io_num = scl_pin,
        .clk_source = I2C_CLK_SRC_DEFAULT,
        .glitch_ignore_cnt = 7,
        .intr_priority = 0,
        .trans_queue_depth = 0,
        .flags = {
            .enable_internal_pullup = true,
        },
    };
    
    esp_err_t err = i2c_new_master_bus(&i2c_bus_conf, &mcp_i2c_handle);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "Erreur création bus I2C MCP23017: %s", esp_err_to_name(err));
        return false;
    }

    ESP_LOGI(TAG, "Bus I2C MCP23017 initialisé (SCL: %d, SDA: %d)", scl_pin, sda_pin);
    return true;
}

// Initialisation du MCP23017
bool MCP23017Switches::initMCP23017()
{
    // Configurer le MCP23017
    // Tous les pins en entrée
    if (!writeRegister(MCP23017_IODIRA, 0xFF) || !writeRegister(MCP23017_IODIRB, 0xFF)) {
        ESP_LOGE(TAG, "Erreur de configuration des directions des pins");
        return false;
    }

    // Activer les pull-ups internes
    if (!writeRegister(MCP23017_GPPUA, 0xFF) || !writeRegister(MCP23017_GPPUB, 0xFF)) {
        ESP_LOGE(TAG, "Erreur de configuration des pull-ups");
        return false;
    }

    // Désactiver les interruptions (mode polling uniquement)
    if (!writeRegister(MCP23017_GPINTENA, 0x00) || !writeRegister(MCP23017_GPINTENB, 0x00)) {
        ESP_LOGE(TAG, "Erreur de désactivation des interruptions");
        return false;
    }

    ESP_LOGI(TAG, "MCP23017 initialisé avec succès en mode polling");
    return true;
}

// Lecture d'un registre du MCP23017
bool MCP23017Switches::readRegister(uint8_t reg, uint8_t* data)
{
    if (mcp_i2c_handle == NULL) {
        ESP_LOGE(TAG, "Handle I2C MCP23017 non initialisé");
        return false;
    }

    // Configuration du device I2C
    i2c_device_config_t dev_cfg = {
        .dev_addr_length = I2C_ADDR_BIT_LEN_7,
        .device_address = mcp_addr,
        .scl_speed_hz = 100000,
        .scl_wait_us = 0,
        .flags = {},
    };

    i2c_master_dev_handle_t dev_handle;
    esp_err_t err = i2c_master_bus_add_device(mcp_i2c_handle, &dev_cfg, &dev_handle);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "Erreur ajout device I2C: %s", esp_err_to_name(err));
        return false;
    }

    // Écrire l'adresse du registre puis lire la donnée
    err = i2c_master_transmit_receive(dev_handle, &reg, 1, data, 1, pdMS_TO_TICKS(100));
    
    // Supprimer le device
    i2c_master_bus_rm_device(dev_handle);
    
    return (err == ESP_OK);
}

// Écriture dans un registre du MCP23017
bool MCP23017Switches::writeRegister(uint8_t reg, uint8_t data)
{
    if (mcp_i2c_handle == NULL) {
        ESP_LOGE(TAG, "Handle I2C MCP23017 non initialisé");
        return false;
    }

    // Configuration du device I2C
    i2c_device_config_t dev_cfg = {
        .dev_addr_length = I2C_ADDR_BIT_LEN_7,
        .device_address = mcp_addr,
        .scl_speed_hz = 100000,
        .scl_wait_us = 0,
        .flags = {},
    };

    i2c_master_dev_handle_t dev_handle;
    esp_err_t err = i2c_master_bus_add_device(mcp_i2c_handle, &dev_cfg, &dev_handle);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "Erreur ajout device I2C: %s", esp_err_to_name(err));
        return false;
    }

    // Préparer les données à écrire (registre + donnée)
    uint8_t write_data[2] = {reg, data};
    err = i2c_master_transmit(dev_handle, write_data, 2, pdMS_TO_TICKS(100));
    
    // Supprimer le device
    i2c_master_bus_rm_device(dev_handle);
    
    return (err == ESP_OK);
}

// Lecture de l'état de tous les switchs
uint16_t MCP23017Switches::readAllSwitches()
{
    uint8_t portA, portB;
    
    if (!readRegister(MCP23017_GPIOA, &portA) || !readRegister(MCP23017_GPIOB, &portB)) {
        ESP_LOGE(TAG, "Erreur de lecture des ports GPIO");
        return current_states; // Retourner l'état précédent en cas d'erreur
    }
    
    // Inverser les bits car les switchs sont actifs bas (pull-up)
    uint16_t states = ~((uint16_t)portB << 8 | portA);
    
    return states;
}

// Obtient l'état actuel d'un switch
bool MCP23017Switches::getSwitchState(size_t index) const
{
    if (index >= 16) {
        ESP_LOGE(TAG, "Index de switch invalide: %zu", index);
        return false;
    }
    
    if (xSemaphoreTake(state_mutex, pdMS_TO_TICKS(10)) == pdTRUE) {
        bool state = (current_states >> index) & 0x01;
        xSemaphoreGive(state_mutex);
        return state;
    }
    
    return false;
}

// Obtient l'état de tous les switchs
uint16_t MCP23017Switches::getAllSwitchStates() const
{
    if (xSemaphoreTake(state_mutex, pdMS_TO_TICKS(10)) == pdTRUE) {
        uint16_t states = current_states;
        xSemaphoreGive(state_mutex);
        return states;
    }
    
    return 0;
}

// Définit une fonction de rappel pour un switch
void MCP23017Switches::setSwitchCallback(size_t index, std::function<void(size_t, bool)> callback)
{
    if (index >= 16) {
        ESP_LOGE(TAG, "Index de switch invalide: %zu", index);
        return;
    }
    
    callbacks[index] = callback;
}

// Tâche de lecture périodique
void MCP23017Switches::readTask(void* pvParameters)
{
    MCP23017Switches* switches = static_cast<MCP23017Switches*>(pvParameters);
    
    ESP_LOGI(TAG, "Tâche de lecture des switchs démarrée");
    
    while (true) {
        uint16_t new_states = switches->readAllSwitches();
        
        if (xSemaphoreTake(switches->state_mutex, pdMS_TO_TICKS(10)) == pdTRUE) {
            if (new_states != switches->current_states) {
                switches->previous_states = switches->current_states;
                switches->current_states = new_states;
                
                // Traiter les changements d'état
                switches->processStateChanges(new_states);
            }
            xSemaphoreGive(switches->state_mutex);
        }
        
        vTaskDelay(pdMS_TO_TICKS(10)); // Lecture toutes les 10ms
    }
}

// Traitement des changements d'état
void MCP23017Switches::processStateChanges(uint16_t new_states)
{
    uint16_t changed = new_states ^ previous_states;
    
    for (size_t i = 0; i < 16; i++) {
        if (changed & (1 << i)) {
            bool new_state = (new_states >> i) & 0x01;
            
            if (logsEnabled) {
                ESP_LOGI(TAG, "Switch %zu: %s", i, new_state ? "PRESSÉ" : "RELÂCHÉ");
            }
            
            // Envoyer un message au format "b,<index sur deux chiffres>,<0 ou 1>" via le communication manager
            char message[10];
            snprintf(message, sizeof(message), "b,%02zu,%d", i, new_state ? 1 : 0);
            sendMessage(message);
            
            // Appeler la fonction de rappel si elle existe
            if (callbacks[i]) {
                callbacks[i](i, new_state);
            }
        }
    }
}