#pragma once

#include "bsp/esp-bsp.h"
#include "driver/gpio.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/semphr.h"
#include "esp_log.h"
#include "esp_system.h"
#include <functional>
#include <vector>

// Déclaration de la fonction externe pour envoyer des messages via le communication manager
#ifdef __cplusplus
extern "C" {
#endif
    extern void sendMessage(const char *message);
#ifdef __cplusplus
}
#endif

// Registres du MCP23017
#define MCP23017_IODIRA     0x00  // Direction des pins du port A (1=input, 0=output)
#define MCP23017_IODIRB     0x01  // Direction des pins du port B (1=input, 0=output)
#define MCP23017_IPOLA      0x02  // Polarité du port A (1=inverse, 0=normal)
#define MCP23017_IPOLB      0x03  // Polarité du port B (1=inverse, 0=normal)
#define MCP23017_GPINTENA   0x04  // Interruption sur changement port A
#define MCP23017_GPINTENB   0x05  // Interruption sur changement port B
#define MCP23017_DEFVALA    0x06  // Valeur par défaut port A
#define MCP23017_DEFVALB    0x07  // Valeur par défaut port B
#define MCP23017_INTCONA    0x08  // Contrôle d'interruption port A
#define MCP23017_INTCONB    0x09  // Contrôle d'interruption port B
#define MCP23017_IOCONA     0x0A  // Configuration I/O
#define MCP23017_IOCONB     0x0B  // Configuration I/O
#define MCP23017_GPPUA      0x0C  // Pull-up port A
#define MCP23017_GPPUB      0x0D  // Pull-up port B
#define MCP23017_INTFA      0x0E  // Flag d'interruption port A
#define MCP23017_INTFB      0x0F  // Flag d'interruption port B
#define MCP23017_INTCAPA    0x10  // Capture d'interruption port A
#define MCP23017_INTCAPB    0x11  // Capture d'interruption port B
#define MCP23017_GPIOA      0x12  // Registre GPIO port A
#define MCP23017_GPIOB      0x13  // Registre GPIO port B
#define MCP23017_OLATA      0x14  // Latch de sortie port A
#define MCP23017_OLATB      0x15  // Latch de sortie port B

// Adresse I2C par défaut du MCP23017
#define MCP23017_DEFAULT_ADDR 0x20

/**
 * @brief Initialise le système de switchs MCP23017 avec une configuration par défaut
 *
 * Cette fonction initialise le système de switchs avec 16 switchs connectés
 * sur un MCP23017 via I2C.
 *
 * @return true si l'initialisation a réussi, false sinon
 */
bool mcp23017_switches_init();

/**
 * @brief Active ou désactive les logs des switchs
 *
 * @param enable true pour activer les logs, false pour les désactiver
 */
void mcp23017_switches_logs_enable(bool enable);

/**
 * @brief Vérifie si les logs des switchs sont activés
 *
 * @return true si les logs sont activés, false sinon
 */
bool mcp23017_switches_logs_are_enabled();

/**
 * @brief Obtient l'état actuel d'un switch par son index
 *
 * @param index Index du switch (0 à 15)
 * @return bool État du switch (true = pressé, false = relâché)
 */
bool mcp23017_switch_get_state(size_t index);

/**
 * @brief Obtient l'état de tous les switchs
 *
 * @return uint16_t État de tous les switchs (bit 0 = switch 0, bit 15 = switch 15)
 */
uint16_t mcp23017_switches_get_all_states();

/**
 * @brief Classe qui encapsule le système de switchs MCP23017
 *
 * Cette classe gère l'initialisation et la lecture de 16 switchs connectés
 * à un MCP23017 via I2C. Elle crée et gère les tâches FreeRTOS nécessaires
 * et fournit une interface simple pour accéder aux états des switchs.
 */
class MCP23017Switches {
public:
    // Variable statique pour contrôler l'activation des logs
    static bool logsEnabled;

    /**
     * @brief Constructeur
     *
     * @param i2c_port Port I2C à utiliser
     * @param sda_pin Pin SDA (GPIO33 par défaut)
     * @param scl_pin Pin SCL (GPIO32 par défaut)
     * @param addr Adresse I2C du MCP23017 (0x20 par défaut)
     */
    MCP23017Switches(i2c_port_t i2c_port = I2C_NUM_0,
                     gpio_num_t sda_pin = GPIO_NUM_33,
                     gpio_num_t scl_pin = GPIO_NUM_32,
                     uint8_t addr = 0x27);

    /**
     * @brief Destructeur
     */
    ~MCP23017Switches();

    /**
     * @brief Initialise le système de switchs
     *
     * @return true si l'initialisation a réussi, false sinon
     */
    bool begin();

    /**
     * @brief Obtient l'état actuel d'un switch
     *
     * @param index Index du switch (0 à 15)
     * @return bool État du switch (true = pressé, false = relâché)
     */
    bool getSwitchState(size_t index) const;

    /**
     * @brief Obtient l'état de tous les switchs
     *
     * @return uint16_t État de tous les switchs
     */
    uint16_t getAllSwitchStates() const;

    /**
     * @brief Définit une fonction de rappel pour un switch
     *
     * @param index Index du switch
     * @param callback Fonction de rappel appelée lors du changement d'état
     */
    void setSwitchCallback(size_t index, std::function<void(size_t, bool)> callback);

private:
    i2c_port_t i2c_port;
    gpio_num_t sda_pin;
    gpio_num_t scl_pin;
    uint8_t mcp_addr;
    i2c_master_bus_handle_t mcp_i2c_handle;  // Handle du bus I2C pour le MCP23017
    
    uint16_t current_states;  // État actuel des switchs
    uint16_t previous_states; // État précédent des switchs
    
    TaskHandle_t read_task_handle;
    
    std::vector<std::function<void(size_t, bool)>> callbacks;  // Fonctions de rappel
    
    SemaphoreHandle_t state_mutex;  // Mutex pour protéger l'accès aux états
    
    /**
     * @brief Initialise le bus I2C
     *
     * @return true si l'initialisation a réussi, false sinon
     */
    bool initI2C();
    
    /**
     * @brief Initialise le MCP23017
     *
     * @return true si l'initialisation a réussi, false sinon
     */
    bool initMCP23017();
    
    /**
     * @brief Lit un registre du MCP23017
     *
     * @param reg Adresse du registre
     * @param data Pointeur vers la donnée lue
     * @return true si la lecture a réussi, false sinon
     */
    bool readRegister(uint8_t reg, uint8_t* data);
    
    /**
     * @brief Écrit dans un registre du MCP23017
     *
     * @param reg Adresse du registre
     * @param data Donnée à écrire
     * @return true si l'écriture a réussi, false sinon
     */
    bool writeRegister(uint8_t reg, uint8_t data);
    
    /**
     * @brief Lit l'état de tous les switchs
     *
     * @return uint16_t État de tous les switchs
     */
    uint16_t readAllSwitches();
    
    /**
     * @brief Tâche FreeRTOS pour lire périodiquement les switchs
     */
    static void readTask(void* pvParameters);
    
    /**
     * @brief Traite les changements d'état des switchs
     *
     * @param new_states Nouveaux états des switchs
     */
    void processStateChanges(uint16_t new_states);
};